import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  ScrollView,
  Alert,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';

import { OnboardingStackParamList } from '../../types/navigation';
import { useTheme } from '../../hooks/useTheme';
import { useUserProfile } from '../../hooks/useUserProfile';
import { Container, Button, Input } from '../../components';
import { useWelcomeScreenStyles } from '../../theme';

type ProfileSetupScreenNavigationProp = StackNavigationProp<
  OnboardingStackParamList,
  'ProfileSetup'
>;

type ProfileSetupScreenRouteProp = RouteProp<
  OnboardingStackParamList,
  'ProfileSetup'
>;

interface Props {
  navigation: ProfileSetupScreenNavigationProp;
  route: ProfileSetupScreenRouteProp;
}

interface FormData {
  name: string;
  age: string;
  height: string;
  weight: string;
  fitnessLevel: 'beginner' | 'intermediate' | 'advanced' | null;
}

interface FormErrors {
  name?: string;
  age?: string;
  height?: string;
  weight?: string;
  fitnessLevel?: string;
}

const ProfileSetupScreen: React.FC<Props> = ({ navigation }) => {
  const { theme } = useTheme();
  const { updateProfile } = useUserProfile();
  const welcomeStyles = useWelcomeScreenStyles(theme);
  
  const [formData, setFormData] = useState<FormData>({
    name: '',
    age: '',
    height: '',
    weight: '',
    fitnessLevel: null,
  });
  
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);

  const fitnessLevels = [
    {
      key: 'beginner',
      title: 'Beginner',
      subtitle: 'New to fitness or returning after a break',
      emoji: '🌱',
    },
    {
      key: 'intermediate',
      title: 'Intermediate',
      subtitle: 'Regular exercise routine for 6+ months',
      emoji: '💪',
    },
    {
      key: 'advanced',
      title: 'Advanced',
      subtitle: 'Experienced with consistent training',
      emoji: '🏋️',
    },
  ];

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.age.trim()) {
      newErrors.age = 'Age is required';
    } else {
      const age = parseInt(formData.age);
      if (isNaN(age) || age < 13 || age > 120) {
        newErrors.age = 'Please enter a valid age (13-120)';
      }
    }

    if (!formData.height.trim()) {
      newErrors.height = 'Height is required';
    } else {
      const height = parseFloat(formData.height);
      if (isNaN(height) || height < 100 || height > 250) {
        newErrors.height = 'Please enter a valid height (100-250 cm)';
      }
    }

    if (!formData.weight.trim()) {
      newErrors.weight = 'Weight is required';
    } else {
      const weight = parseFloat(formData.weight);
      if (isNaN(weight) || weight < 30 || weight > 300) {
        newErrors.weight = 'Please enter a valid weight (30-300 kg)';
      }
    }

    if (!formData.fitnessLevel) {
      newErrors.fitnessLevel = 'Please select your fitness level';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleContinue = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await updateProfile({
        name: formData.name.trim(),
        age: parseInt(formData.age),
        height: parseFloat(formData.height),
        weight: parseFloat(formData.weight),
        fitnessLevel: formData.fitnessLevel!,
      });
      
      navigation.navigate('GoalSelection');
    } catch (error) {
      Alert.alert('Error', 'Failed to save profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const updateFormData = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const selectFitnessLevel = (level: FormData['fitnessLevel']) => {
    setFormData(prev => ({ ...prev, fitnessLevel: level }));
    if (errors.fitnessLevel) {
      setErrors(prev => ({ ...prev, fitnessLevel: undefined }));
    }
  };

  const styles = createStyles(theme);

  return (
    <LinearGradient
      colors={theme.colors.welcomeGradient}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        <StatusBar
          barStyle={theme.colors.statusBarStyle}
          backgroundColor="transparent"
          translucent={true}
        />

        {/* Custom Title Bar with Darker Gradient */}
        <LinearGradient
          colors={[
            'rgba(102, 126, 234, 0.95)', // Slightly darker version of gradient start
            'rgba(118, 75, 162, 0.90)',  // Slightly darker version of gradient middle
            'rgba(240, 147, 251, 0.85)'  // Slightly darker version of gradient end
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.titleBar}
        >
          <View style={styles.titleBarContent}>
            <Text style={[styles.titleBarText, welcomeStyles.text.title]}>
              Profile Setup
            </Text>
          </View>
        </LinearGradient>

        <Container variant="safe" style={styles.content} backgroundColor="transparent">
        <ScrollView 
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header */}
          <View style={styles.header}>
            <Text style={[styles.subtitle, welcomeStyles.text.subtitle]}>
              This information helps us create a personalized fitness plan for you
            </Text>
          </View>

          {/* Form */}
          <View style={[styles.form, welcomeStyles.glassMorphism.container]}>
            {/* Name Input */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>What's your name?</Text>
              <Input
                placeholder="Enter your name"
                value={formData.name}
                onChangeText={(value) => updateFormData('name', value)}
                error={errors.name}
                autoCapitalize="words"
                autoCorrect={false}
              />
            </View>

            {/* Age Input */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>How old are you?</Text>
              <Input
                placeholder="Enter your age"
                value={formData.age}
                onChangeText={(value) => updateFormData('age', value)}
                error={errors.age}
                keyboardType="numeric"
                maxLength={3}
              />
            </View>

            {/* Height Input */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>What's your height? (cm)</Text>
              <Input
                placeholder="Enter your height in cm"
                value={formData.height}
                onChangeText={(value) => updateFormData('height', value)}
                error={errors.height}
                keyboardType="numeric"
                maxLength={3}
              />
            </View>

            {/* Weight Input */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>What's your weight? (kg)</Text>
              <Input
                placeholder="Enter your weight in kg"
                value={formData.weight}
                onChangeText={(value) => updateFormData('weight', value)}
                error={errors.weight}
                keyboardType="numeric"
                maxLength={5}
              />
            </View>

            {/* Fitness Level Selection */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>What's your fitness level?</Text>
              {errors.fitnessLevel && (
                <Text style={styles.errorText}>{errors.fitnessLevel}</Text>
              )}
              
              <View style={styles.activityContainer}>
                {fitnessLevels.map((level) => {
                  const isSelected = formData.fitnessLevel === level.key;
                  return (
                    <Button
                      key={level.key}
                      title={`${level.emoji} ${level.title}`}
                      variant={isSelected ? 'primary' : 'outline'}
                      size="medium"
                      onPress={() => selectFitnessLevel(level.key as FormData['fitnessLevel'])}
                      style={StyleSheet.flatten([
                        styles.activityButton,
                        isSelected && styles.selectedActivity,
                      ])}
                      textStyle={StyleSheet.flatten([
                        styles.activityButtonText,
                        isSelected && styles.selectedActivityText,
                      ])}
                    />
                  );
                })}
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Continue Button */}
        <View style={styles.footer}>
          <Button
            title="Continue"
            variant="primary"
            size="large"
            onPress={handleContinue}
            loading={isLoading}
            disabled={isLoading}
            style={styles.continueButton}
          />
        </View></Container>
      </SafeAreaView>
    </LinearGradient>
  );
};

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    titleBar: {
      paddingTop: theme.spacing.md,
      paddingBottom: theme.spacing.sm,
      paddingHorizontal: theme.spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    },
    titleBarContent: {
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 44, // Standard navigation bar height
    },
    titleBarText: {
      fontSize: theme.typography.fontSize.lg,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.textInverse,
      textAlign: 'center',
      ...theme.welcomeScreen.textShadow,
    },
    content: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
      paddingHorizontal: theme.spacing.lg,
    },
    header: {
      paddingTop: theme.spacing.lg,
      paddingBottom: theme.spacing.lg,
      alignItems: 'center',
    },
    title: {
      fontSize: theme.typography.fontSize['2xl'],
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.base,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
    },
    form: {
      paddingBottom: theme.spacing.xl,
    },
    inputGroup: {
      marginBottom: theme.spacing.lg,
    },
    label: {
      fontSize: theme.typography.fontSize.base,
      fontFamily: theme.typography.fontFamily.medium,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    errorText: {
      fontSize: theme.typography.fontSize.sm,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.error,
      marginTop: theme.spacing.xs,
    },
    activityContainer: {
      gap: theme.spacing.sm,
    },
    activityButton: {
      height: 60,
      justifyContent: 'flex-start',
      paddingHorizontal: theme.spacing.md,
    },
    selectedActivity: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.primary,
    },
    activityButtonText: {
      fontSize: theme.typography.fontSize.base,
      fontFamily: theme.typography.fontFamily.medium,
      textAlign: 'left',
    },
    selectedActivityText: {
      color: theme.colors.textInverse,
    },
    footer: {
      paddingHorizontal: theme.spacing.lg,
      paddingTop: theme.spacing.md,
      paddingBottom: theme.spacing.lg,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    continueButton: {
      width: '100%',
    },
  });

export default ProfileSetupScreen;