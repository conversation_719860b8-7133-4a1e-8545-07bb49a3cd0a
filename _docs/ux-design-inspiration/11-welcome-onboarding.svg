<svg width="375" height="812" viewBox="0 0 375 812" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Gradient -->
  <defs>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.9" />
    </linearGradient>
  </defs>
  
  <rect width="375" height="812" fill="url(#backgroundGradient)"/>
  
  <!-- Decorative Elements -->
  <circle cx="320" cy="100" r="60" fill="rgba(255,255,255,0.1)"/>
  <circle cx="50" cy="200" r="30" fill="rgba(255,255,255,0.08)"/>
  <circle cx="350" cy="300" r="40" fill="rgba(255,255,255,0.06)"/>
  
  <!-- Status Bar -->
  <rect width="375" height="44" fill="transparent"/>
  <text x="24" y="30" font-family="SF Pro Display" font-size="14" font-weight="600" fill="white">9:41</text>
  <text x="351" y="30" font-family="SF Pro Display" font-size="14" font-weight="400" fill="white" text-anchor="end">100%</text>
  
  <!-- Logo/Brand Section -->
  <circle cx="187" cy="150" r="40" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
  <text x="187" y="160" font-family="SF Pro Display" font-size="28" font-weight="400" fill="white" text-anchor="middle">💪</text>
  
  <text x="187" y="220" font-family="SF Pro Display" font-size="32" font-weight="700" fill="white" text-anchor="middle">My Fitness Coach</text>
  <text x="187" y="245" font-family="SF Pro Display" font-size="16" font-weight="400" fill="rgba(255,255,255,0.9)" text-anchor="middle">Your personalized fitness journey starts here</text>
  
  <!-- Main Content Card -->
  <rect x="24" y="300" width="327" height="420" rx="24" fill="url(#cardGradient)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
  
  <!-- Welcome Message -->
  <text x="187" y="340" font-family="SF Pro Display" font-size="24" font-weight="700" fill="#1A1A1A" text-anchor="middle">Welcome!</text>
  <text x="187" y="365" font-family="SF Pro Display" font-size="16" font-weight="400" fill="#6B7280" text-anchor="middle">Transform your health with AI-powered</text>
  <text x="187" y="385" font-family="SF Pro Display" font-size="16" font-weight="400" fill="#6B7280" text-anchor="middle">coaching and personalized workouts</text>
  
  <!-- Feature Highlights -->
  <g>
    <!-- Feature 1 -->
    <circle cx="60" cy="430" r="20" fill="#DBEAFE"/>
    <text x="60" y="437" font-family="SF Pro Display" font-size="16" font-weight="400" fill="#3B82F6" text-anchor="middle">🎯</text>
    <text x="95" y="425" font-family="SF Pro Display" font-size="14" font-weight="600" fill="#1A1A1A">Personalized Goals</text>
    <text x="95" y="440" font-family="SF Pro Display" font-size="12" font-weight="400" fill="#6B7280">AI creates custom fitness plans just for you</text>
    
    <!-- Feature 2 -->
    <circle cx="60" cy="480" r="20" fill="#DCFCE7"/>
    <text x="60" y="487" font-family="SF Pro Display" font-size="16" font-weight="400" fill="#16A34A" text-anchor="middle">📊</text>
    <text x="95" y="475" font-family="SF Pro Display" font-size="14" font-weight="600" fill="#1A1A1A">Progress Tracking</text>
    <text x="95" y="490" font-family="SF Pro Display" font-size="12" font-weight="400" fill="#6B7280">Monitor your journey with detailed analytics</text>
    
    <!-- Feature 3 -->
    <circle cx="60" cy="530" r="20" fill="#FEF3C7"/>
    <text x="60" y="537" font-family="SF Pro Display" font-size="16" font-weight="400" fill="#F59E0B" text-anchor="middle">👥</text>
    <text x="95" y="525" font-family="SF Pro Display" font-size="14" font-weight="600" fill="#1A1A1A">Community Support</text>
    <text x="95" y="540" font-family="SF Pro Display" font-size="12" font-weight="400" fill="#6B7280">Connect with like-minded fitness enthusiasts</text>
  </g>
  
  <!-- Social Proof -->
  <rect x="50" y="570" width="275" height="60" rx="16" fill="#F8FAFC" stroke="#E2E8F0" stroke-width="1"/>
  
  <!-- User Avatars -->
  <circle cx="75" cy="590" r="12" fill="#3B82F6"/>
  <text x="75" y="595" font-family="SF Pro Display" font-size="8" font-weight="600" fill="white" text-anchor="middle">A</text>
  
  <circle cx="95" cy="590" r="12" fill="#10B981"/>
  <text x="95" y="595" font-family="SF Pro Display" font-size="8" font-weight="600" fill="white" text-anchor="middle">S</text>
  
  <circle cx="115" cy="590" r="12" fill="#F59E0B"/>
  <text x="115" y="595" font-family="SF Pro Display" font-size="8" font-weight="600" fill="white" text-anchor="middle">M</text>
  
  <circle cx="135" cy="590" r="12" fill="#EF4444"/>
  <text x="135" y="595" font-family="SF Pro Display" font-size="8" font-weight="600" fill="white" text-anchor="middle">J</text>
  
  <!-- Social Proof Text -->
  <text x="75" y="610" font-family="SF Pro Display" font-size="12" font-weight="600" fill="#1A1A1A">Join 50,000+ users</text>
  <text x="75" y="625" font-family="SF Pro Display" font-size="10" font-weight="400" fill="#6B7280">who achieved their fitness goals with us</text>
  
  <!-- Rating Stars -->
  <text x="250" y="595" font-family="SF Pro Display" font-size="14" font-weight="400" fill="#F59E0B">⭐⭐⭐⭐⭐</text>
  <text x="250" y="610" font-family="SF Pro Display" font-size="12" font-weight="500" fill="#1A1A1A">4.9/5 Rating</text>
  <text x="250" y="625" font-family="SF Pro Display" font-size="10" font-weight="400" fill="#6B7280">12,000+ reviews</text>
  
  <!-- CTA Buttons -->
  <rect x="50" y="650" width="275" height="50" rx="25" fill="#3B82F6" stroke="#2563EB" stroke-width="1"/>
  <text x="187" y="680" font-family="SF Pro Display" font-size="16" font-weight="600" fill="white" text-anchor="middle">Get Started</text>
  
  <!-- Secondary Action -->
  <text x="187" y="730" font-family="SF Pro Display" font-size="14" font-weight="500" fill="rgba(255,255,255,0.8)" text-anchor="middle">Already have an account? Sign In</text>
  
  <!-- Bottom Decorative Elements -->
  <circle cx="30" cy="750" r="15" fill="rgba(255,255,255,0.1)"/>
  <circle cx="345" cy="780" r="20" fill="rgba(255,255,255,0.08)"/>
  
  <!-- Privacy Notice -->
  <text x="187" y="770" font-family="SF Pro Display" font-size="10" font-weight="400" fill="rgba(255,255,255,0.7)" text-anchor="middle">By continuing, you agree to our Terms of Service and Privacy Policy</text>
</svg>