<svg width="375" height="812" viewBox="0 0 375 812" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="375" height="812" fill="#F8F9FA"/>
  
  <!-- Top Section with Gradient -->
  <defs>
    <linearGradient id="topGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="buttonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563EB;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="375" height="400" fill="url(#topGradient)"/>
  
  <!-- Decorative Shapes -->
  <circle cx="320" cy="80" r="40" fill="rgba(255,255,255,0.1)"/>
  <circle cx="60" cy="120" r="25" fill="rgba(255,255,255,0.08)"/>
  <rect x="300" y="200" width="60" height="60" rx="30" fill="rgba(255,255,255,0.06)" transform="rotate(45 330 230)"/>
  
  <!-- Status Bar -->
  <rect width="375" height="44" fill="transparent"/>
  <text x="24" y="30" font-family="SF Pro Display" font-size="14" font-weight="600" fill="white">9:41</text>
  <text x="351" y="30" font-family="SF Pro Display" font-size="14" font-weight="400" fill="white" text-anchor="end">100%</text>
  
  <!-- Hero Illustration Area -->
  <rect x="87" y="100" width="200" height="200" rx="100" fill="rgba(255,255,255,0.15)"/>
  
  <!-- Central Icon/Illustration -->
  <circle cx="187" cy="200" r="60" fill="rgba(255,255,255,0.2)"/>
  <text x="187" y="220" font-family="SF Pro Display" font-size="48" font-weight="400" fill="white" text-anchor="middle">🏃‍♀️</text>
  
  <!-- Floating Elements Around Hero -->
  <circle cx="120" cy="150" r="20" fill="rgba(255,255,255,0.2)"/>
  <text x="120" y="158" font-family="SF Pro Display" font-size="16" font-weight="400" fill="white" text-anchor="middle">💪</text>
  
  <circle cx="254" cy="160" r="18" fill="rgba(255,255,255,0.2)"/>
  <text x="254" y="167" font-family="SF Pro Display" font-size="14" font-weight="400" fill="white" text-anchor="middle">🎯</text>
  
  <circle cx="130" cy="250" r="16" fill="rgba(255,255,255,0.2)"/>
  <text x="130" y="257" font-family="SF Pro Display" font-size="12" font-weight="400" fill="white" text-anchor="middle">📊</text>
  
  <circle cx="244" cy="240" r="22" fill="rgba(255,255,255,0.2)"/>
  <text x="244" y="248" font-family="SF Pro Display" font-size="18" font-weight="400" fill="white" text-anchor="middle">🔥</text>
  
  <!-- App Title -->
  <text x="187" y="340" font-family="SF Pro Display" font-size="28" font-weight="700" fill="white" text-anchor="middle">My Fitness Coach</text>
  <text x="187" y="365" font-family="SF Pro Display" font-size="16" font-weight="400" fill="rgba(255,255,255,0.9)" text-anchor="middle">Your AI-powered fitness companion</text>
  
  <!-- Main Content Card -->
  <rect x="24" y="420" width="327" height="340" rx="20" fill="white" stroke="#E5E7EB" stroke-width="1"/>
  
  <!-- Welcome Content -->
  <text x="187" y="460" font-family="SF Pro Display" font-size="24" font-weight="700" fill="#1A1A1A" text-anchor="middle">Welcome to Your</text>
  <text x="187" y="485" font-family="SF Pro Display" font-size="24" font-weight="700" fill="#3B82F6" text-anchor="middle">Fitness Journey</text>
  
  <text x="187" y="515" font-family="SF Pro Display" font-size="14" font-weight="400" fill="#6B7280" text-anchor="middle">Discover personalized workouts, track your progress,</text>
  <text x="187" y="535" font-family="SF Pro Display" font-size="14" font-weight="400" fill="#6B7280" text-anchor="middle">and achieve your fitness goals with AI guidance.</text>
  
  <!-- Feature Cards -->
  <rect x="50" y="560" width="90" height="80" rx="12" fill="#F0F9FF" stroke="#BAE6FD" stroke-width="1"/>
  <text x="95" y="585" font-family="SF Pro Display" font-size="20" font-weight="400" fill="#0EA5E9" text-anchor="middle">🤖</text>
  <text x="95" y="605" font-family="SF Pro Display" font-size="12" font-weight="600" fill="#0369A1" text-anchor="middle">AI Coach</text>
  <text x="95" y="620" font-family="SF Pro Display" font-size="10" font-weight="400" fill="#0369A1" text-anchor="middle">Smart guidance</text>
  <text x="95" y="632" font-family="SF Pro Display" font-size="10" font-weight="400" fill="#0369A1" text-anchor="middle">for every workout</text>
  
  <rect x="142" y="560" width="90" height="80" rx="12" fill="#F0FDF4" stroke="#BBF7D0" stroke-width="1"/>
  <text x="187" y="585" font-family="SF Pro Display" font-size="20" font-weight="400" fill="#16A34A" text-anchor="middle">📈</text>
  <text x="187" y="605" font-family="SF Pro Display" font-size="12" font-weight="600" fill="#15803D" text-anchor="middle">Progress</text>
  <text x="187" y="620" font-family="SF Pro Display" font-size="10" font-weight="400" fill="#15803D" text-anchor="middle">Track every</text>
  <text x="187" y="632" font-family="SF Pro Display" font-size="10" font-weight="400" fill="#15803D" text-anchor="middle">milestone</text>
  
  <rect x="234" y="560" width="90" height="80" rx="12" fill="#FEF3C7" stroke="#FDE68A" stroke-width="1"/>
  <text x="279" y="585" font-family="SF Pro Display" font-size="20" font-weight="400" fill="#F59E0B" text-anchor="middle">🏆</text>
  <text x="279" y="605" font-family="SF Pro Display" font-size="12" font-weight="600" fill="#D97706" text-anchor="middle">Achieve</text>
  <text x="279" y="620" font-family="SF Pro Display" font-size="10" font-weight="400" fill="#D97706" text-anchor="middle">Unlock rewards</text>
  <text x="279" y="632" font-family="SF Pro Display" font-size="10" font-weight="400" fill="#D97706" text-anchor="middle">& badges</text>
  
  <!-- Stats Preview -->
  <rect x="50" y="660" width="274" height="40" rx="20" fill="#F8FAFC"/>
  <text x="70" y="675" font-family="SF Pro Display" font-size="10" font-weight="500" fill="#6B7280">🔥 50K+ Active Users</text>
  <text x="70" y="690" font-family="SF Pro Display" font-size="10" font-weight="500" fill="#6B7280">⭐ 4.9 App Store Rating</text>
  
  <text x="200" y="675" font-family="SF Pro Display" font-size="10" font-weight="500" fill="#6B7280">💪 1M+ Workouts Completed</text>
  <text x="200" y="690" font-family="SF Pro Display" font-size="10" font-weight="500" fill="#6B7280">🎯 95% Goal Achievement Rate</text>
  
  <!-- CTA Button -->
  <rect x="50" y="720" width="274" height="50" rx="25" fill="url(#buttonGradient)"/>
  <text x="187" y="750" font-family="SF Pro Display" font-size="16" font-weight="600" fill="white" text-anchor="middle">Start Your Journey</text>
  
  <!-- Bottom Links -->
  <text x="187" y="790" font-family="SF Pro Display" font-size="12" font-weight="500" fill="#6B7280" text-anchor="middle">Already have an account?</text>
  <text x="187" y="805" font-family="SF Pro Display" font-size="12" font-weight="600" fill="#3B82F6" text-anchor="middle">Sign In</text>
</svg>