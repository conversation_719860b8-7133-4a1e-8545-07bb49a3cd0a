# My Fitness Coach - Phase 1 Implementation Guide

## 🎯 Overview

This guide provides step-by-step instructions for implementing Phase 1 of the "My Fitness Coach" React Native app. Phase 1 focuses on creating an offline-first MVP with static data, local storage, and no backend dependencies.

---

## 📋 Prerequisites

### Development Environment
- **Node.js**: 18.x or higher
- **React Native CLI**: Latest version
- **React Native**: 0.73+
- **TypeScript**: 5.0+
- **iOS**: Xcode 15+ (for iOS development)
- **Android**: Android Studio with SDK 34+ (for Android development)

### Required Tools
```bash
# Install React Native CLI globally
npm install -g @react-native-community/cli

# Install CocoaPods (for iOS)
sudo gem install cocoapods
```

---

## 🚀 Project Setup

### Step 1: Initialize React Native Project

```bash
# Create new React Native project with TypeScript
npx react-native@latest init MyFitnessCoach --template react-native-template-typescript

cd MyFitnessCoach
```

### Step 2: Install Core Dependencies

```bash
# Navigation
npm install @react-navigation/native @react-navigation/stack @react-navigation/bottom-tabs
npm install react-native-screens react-native-safe-area-context

# State Management
npm install zustand

# UI Components & Styling
npm install native-base react-native-svg
npm install nativewind
npm install --save-dev tailwindcss

# Local Storage
npm install react-native-mmkv
npm install @react-native-async-storage/async-storage

# Forms
npm install react-hook-form

# Images & Media
npm install react-native-fast-image
npm install react-native-vector-icons

# Animations
npm install react-native-reanimated

# Security
npm install crypto-js
npm install --save-dev @types/crypto-js

# Development
npm install --save-dev @types/react-native-vector-icons
```

### Step 3: Platform-specific Setup

#### iOS Setup
```bash
cd ios && pod install && cd ..
```

#### Android Setup
Add to `android/app/build.gradle`:
```gradle
android {
    ...
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }
}
```

### Step 4: Configure NativeWind (Tailwind CSS)

```bash
# Initialize Tailwind CSS
npx tailwindcss init
```

Update `tailwind.config.js`:
```javascript
module.exports = {
  content: ['./App.{js,jsx,ts,tsx}', './src/**/*.{js,jsx,ts,tsx}'],
  theme: {
    extend: {},
  },
  plugins: [],
}
```

Update `babel.config.js`:
```javascript
module.exports = {
  presets: ['module:metro-react-native-babel-preset'],
  plugins: ['nativewind/babel'],
};
```

---

## 📁 Project Structure Setup

### Create Directory Structure

```bash
mkdir -p src/{components/{common,forms,charts,themed},screens/{onboarding,dashboard,workout,nutrition,progress,profile},navigation,store,services,data,utils,hooks,types,constants,assets/{images,videos,fonts},themes}
```

### Core Files to Create

```
src/
├── components/
│   ├── common/
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   └── LoadingSpinner.tsx
│   ├── forms/
│   │   └── ProfileForm.tsx
│   ├── charts/
│   │   └── ProgressChart.tsx
│   └── themed/
│       └── ThemedContainer.tsx
├── screens/
│   ├── onboarding/
│   │   ├── WelcomeScreen.tsx
│   │   ├── ProfileSetupScreen.tsx
│   │   └── GoalSelectionScreen.tsx
│   ├── dashboard/
│   │   └── DashboardScreen.tsx
│   ├── workout/
│   │   ├── WorkoutListScreen.tsx
│   │   ├── WorkoutDetailScreen.tsx
│   │   └── ExerciseDetailScreen.tsx
│   ├── nutrition/
│   │   ├── MealPlanScreen.tsx
│   │   └── MealDetailScreen.tsx
│   ├── progress/
│   │   └── ProgressScreen.tsx
│   └── profile/
│       └── ProfileScreen.tsx
├── navigation/
│   ├── AppNavigator.tsx
│   ├── TabNavigator.tsx
│   └── StackNavigator.tsx
├── store/
│   ├── userStore.ts
│   ├── workoutStore.ts
│   ├── nutritionStore.ts
│   └── progressStore.ts
├── services/
│   ├── mockApi.ts
│   ├── userService.ts
│   ├── workoutService.ts
│   ├── nutritionService.ts
│   ├── storageService.ts
│   └── securityService.ts
├── data/
│   ├── workouts.json
│   ├── exercises.json
│   ├── meals.json
│   └── nutrition.json
├── utils/
│   ├── dateUtils.ts
│   ├── validationUtils.ts
│   └── formatUtils.ts
├── hooks/
│   ├── useUserProfile.ts
│   ├── useWorkouts.ts
│   └── useProgress.ts
├── types/
│   ├── user.ts
│   ├── workout.ts
│   ├── nutrition.ts
│   └── progress.ts
├── constants/
│   ├── colors.ts
│   ├── dimensions.ts
│   └── strings.ts
└── themes/
    ├── male.ts
    ├── female.ts
    └── neutral.ts
```

---

## 🏗️ Core Implementation

### Step 1: TypeScript Interfaces

Create `src/types/user.ts`:
```typescript
export interface UserProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  gender: 'male' | 'female' | 'other';
  dateOfBirth: string;
  heightCm: number;
  weightKg: number;
  activityLevel: 'sedentary' | 'light' | 'moderate' | 'active' | 'very_active';
  fitnessGoal: 'muscle_gain' | 'fat_loss' | 'general_health' | 'endurance';
  preferredWorkoutTime: string;
  workoutFrequency: number;
  themePreference: 'male' | 'female' | 'neutral' | 'auto';
  createdAt: string;
  updatedAt: string;
}

export interface OnboardingData {
  step: number;
  totalSteps: number;
  isCompleted: boolean;
}
```

Create `src/types/workout.ts`:
```typescript
export interface Exercise {
  id: string;
  name: string;
  description: string;
  muscleGroups: string[];
  equipmentNeeded: string[];
  difficultyLevel: 1 | 2 | 3 | 4 | 5;
  videoPath: string;
  animationPath: string;
  instructions: string[];
  sets: number;
  reps: string;
  restTime: number;
  genderSpecific: 'male' | 'female' | 'neutral';
}

export interface WorkoutPlan {
  id: string;
  name: string;
  description: string;
  difficultyLevel: 1 | 2 | 3 | 4 | 5;
  durationWeeks: number;
  targetGoal: string;
  genderSpecific: 'male' | 'female' | 'neutral';
  exercises: Exercise[];
  schedule: WorkoutSchedule[];
}

export interface WorkoutSchedule {
  day: number;
  exercises: string[]; // Exercise IDs
  restDay: boolean;
}
```

### Step 2: Storage Service

Create `src/services/storageService.ts`:
```typescript
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MMKV } from 'react-native-mmkv';

const storage = new MMKV();

export class StorageService {
  // Fast storage for frequently accessed data (MMKV)
  static setItem(key: string, value: string): Promise<void> {
    return new Promise((resolve) => {
      storage.set(key, value);
      resolve();
    });
  }

  static getItem(key: string): Promise<string | null> {
    return new Promise((resolve) => {
      const value = storage.getString(key);
      resolve(value || null);
    });
  }

  static removeItem(key: string): Promise<void> {
    return new Promise((resolve) => {
      storage.delete(key);
      resolve();
    });
  }

  // Slower storage for large data (AsyncStorage)
  static async setLargeItem(key: string, value: string): Promise<void> {
    await AsyncStorage.setItem(key, value);
  }

  static async getLargeItem(key: string): Promise<string | null> {
    return await AsyncStorage.getItem(key);
  }

  // Clear all app data
  static async clearAll(): Promise<void> {
    storage.clearAll();
    await AsyncStorage.clear();
  }
}
```

### Step 3: Zustand Stores

Create `src/store/userStore.ts`:
```typescript
import { create } from 'zustand';
import { UserProfile, OnboardingData } from '../types/user';
import { StorageService } from '../services/storageService';

interface UserState {
  profile: UserProfile | null;
  onboarding: OnboardingData;
  isLoading: boolean;
  
  // Actions
  setProfile: (profile: UserProfile) => void;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  loadProfile: () => Promise<void>;
  setOnboardingStep: (step: number) => void;
  completeOnboarding: () => Promise<void>;
  clearUserData: () => Promise<void>;
}

export const useUserStore = create<UserState>((set, get) => ({
  profile: null,
  onboarding: {
    step: 1,
    totalSteps: 4,
    isCompleted: false,
  },
  isLoading: false,

  setProfile: (profile) => set({ profile }),

  updateProfile: async (updates) => {
    const currentProfile = get().profile;
    if (!currentProfile) return;

    const updatedProfile = {
      ...currentProfile,
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    set({ profile: updatedProfile });
    await StorageService.setItem('userProfile', JSON.stringify(updatedProfile));
  },

  loadProfile: async () => {
    set({ isLoading: true });
    try {
      const profileData = await StorageService.getItem('userProfile');
      const onboardingData = await StorageService.getItem('onboarding');
      
      if (profileData) {
        set({ profile: JSON.parse(profileData) });
      }
      
      if (onboardingData) {
        set({ onboarding: JSON.parse(onboardingData) });
      }
    } catch (error) {
      console.error('Error loading profile:', error);
    } finally {
      set({ isLoading: false });
    }
  },

  setOnboardingStep: (step) => {
    const onboarding = { ...get().onboarding, step };
    set({ onboarding });
    StorageService.setItem('onboarding', JSON.stringify(onboarding));
  },

  completeOnboarding: async () => {
    const onboarding = { ...get().onboarding, isCompleted: true };
    set({ onboarding });
    await StorageService.setItem('onboarding', JSON.stringify(onboarding));
  },

  clearUserData: async () => {
    set({ profile: null, onboarding: { step: 1, totalSteps: 4, isCompleted: false } });
    await StorageService.removeItem('userProfile');
    await StorageService.removeItem('onboarding');
  },
}));
```

### Step 4: Theme System

Create `src/themes/male.ts`:
```typescript
export const maleTheme = {
  colors: {
    primary: '#2563EB', // Blue
    secondary: '#1E40AF',
    accent: '#3B82F6',
    background: '#F8FAFC',
    surface: '#FFFFFF',
    text: '#1E293B',
    textSecondary: '#64748B',
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    border: '#E2E8F0',
  },
  typography: {
    fontFamily: 'System',
    sizes: {
      xs: 12,
      sm: 14,
      md: 16,
      lg: 18,
      xl: 20,
      '2xl': 24,
      '3xl': 30,
    },
    weights: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    '2xl': 48,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
  },
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
  },
};
```

Create `src/themes/female.ts`:
```typescript
export const femaleTheme = {
  colors: {
    primary: '#EC4899', // Pink
    secondary: '#DB2777',
    accent: '#F472B6',
    background: '#FDF2F8',
    surface: '#FFFFFF',
    text: '#1E293B',
    textSecondary: '#64748B',
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    border: '#F3E8FF',
  },
  typography: {
    fontFamily: 'System',
    sizes: {
      xs: 12,
      sm: 14,
      md: 16,
      lg: 18,
      xl: 20,
      '2xl': 24,
      '3xl': 30,
    },
    weights: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    '2xl': 48,
  },
  borderRadius: {
    sm: 6,
    md: 12,
    lg: 18,
    xl: 24,
  },
  shadows: {
    sm: {
      shadowColor: '#EC4899',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#EC4899',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
  },
};
```

### Step 5: Static Data Files

Create `src/data/workouts.json`:
```json
[
  {
    "id": "workout_male_muscle_gain",
    "name": "Muscle Building Program",
    "description": "4-week intensive muscle building program for men",
    "difficultyLevel": 4,
    "durationWeeks": 4,
    "targetGoal": "muscle_gain",
    "genderSpecific": "male",
    "exercises": [
      "exercise_push_ups",
      "exercise_squats",
      "exercise_deadlifts",
      "exercise_bench_press",
      "exercise_pull_ups"
    ],
    "schedule": [
      {
        "day": 1,
        "exercises": ["exercise_push_ups", "exercise_bench_press"],
        "restDay": false
      },
      {
        "day": 2,
        "exercises": ["exercise_squats", "exercise_deadlifts"],
        "restDay": false
      },
      {
        "day": 3,
        "exercises": [],
        "restDay": true
      },
      {
        "day": 4,
        "exercises": ["exercise_pull_ups"],
        "restDay": false
      },
      {
        "day": 5,
        "exercises": ["exercise_push_ups", "exercise_squats"],
        "restDay": false
      },
      {
        "day": 6,
        "exercises": [],
        "restDay": true
      },
      {
        "day": 7,
        "exercises": [],
        "restDay": true
      }
    ]
  }
]
```

Create `src/data/exercises.json`:
```json
[
  {
    "id": "exercise_push_ups",
    "name": "Push-ups",
    "description": "Classic upper body exercise targeting chest, shoulders, and triceps",
    "muscleGroups": ["chest", "shoulders", "triceps"],
    "equipmentNeeded": [],
    "difficultyLevel": 2,
    "videoPath": "assets/videos/pushups.mp4",
    "animationPath": "assets/images/pushups.gif",
    "instructions": [
      "Start in a plank position with hands slightly wider than shoulders",
      "Lower your body until chest nearly touches the floor",
      "Push back up to starting position",
      "Keep your core tight throughout the movement"
    ],
    "sets": 3,
    "reps": "8-12",
    "restTime": 60,
    "genderSpecific": "neutral"
  }
]
```

---

## 🎨 UI Implementation

### Step 1: Common Components

Create `src/components/common/Button.tsx`:
```typescript
import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { useUserStore } from '../../store/userStore';
import { maleTheme } from '../../themes/male';
import { femaleTheme } from '../../themes/female';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
}) => {
  const { profile } = useUserStore();
  const theme = profile?.gender === 'female' ? femaleTheme : maleTheme;

  const getButtonStyle = () => {
    const baseStyle = {
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: size === 'sm' ? theme.spacing.sm : size === 'lg' ? theme.spacing.lg : theme.spacing.md,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      flexDirection: 'row' as const,
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          backgroundColor: disabled ? theme.colors.border : theme.colors.primary,
        };
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: disabled ? theme.colors.border : theme.colors.secondary,
        };
      case 'outline':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: disabled ? theme.colors.border : theme.colors.primary,
        };
      default:
        return baseStyle;
    }
  };

  const getTextStyle = () => {
    const baseStyle = {
      fontSize: size === 'sm' ? theme.typography.sizes.sm : size === 'lg' ? theme.typography.sizes.lg : theme.typography.sizes.md,
      fontWeight: theme.typography.weights.semibold,
    };

    switch (variant) {
      case 'outline':
        return {
          ...baseStyle,
          color: disabled ? theme.colors.textSecondary : theme.colors.primary,
        };
      default:
        return {
          ...baseStyle,
          color: disabled ? theme.colors.textSecondary : '#FFFFFF',
        };
    }
  };

  return (
    <TouchableOpacity
      style={getButtonStyle()}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading && (
        <ActivityIndicator
          size="small"
          color={variant === 'outline' ? theme.colors.primary : '#FFFFFF'}
          style={{ marginRight: theme.spacing.sm }}
        />
      )}
      <Text style={getTextStyle()}>{title}</Text>
    </TouchableOpacity>
  );
};
```

### Step 2: Navigation Setup

Create `src/navigation/AppNavigator.tsx`:
```typescript
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { useUserStore } from '../store/userStore';
import { OnboardingNavigator } from './OnboardingNavigator';
import { TabNavigator } from './TabNavigator';

const Stack = createStackNavigator();

export const AppNavigator: React.FC = () => {
  const { onboarding } = useUserStore();

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!onboarding.isCompleted ? (
          <Stack.Screen name="Onboarding" component={OnboardingNavigator} />
        ) : (
          <Stack.Screen name="Main" component={TabNavigator} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};
```

---

## 🧪 Testing Setup

### Unit Testing

Create `__tests__/stores/userStore.test.ts`:
```typescript
import { useUserStore } from '../../src/store/userStore';
import { UserProfile } from '../../src/types/user';

// Mock the storage service
jest.mock('../../src/services/storageService', () => ({
  StorageService: {
    setItem: jest.fn().mockResolvedValue(undefined),
    getItem: jest.fn().mockResolvedValue(null),
    removeItem: jest.fn().mockResolvedValue(undefined),
  },
}));

describe('UserStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useUserStore.setState({
      profile: null,
      onboarding: { step: 1, totalSteps: 4, isCompleted: false },
      isLoading: false,
    });
  });

  it('should set user profile', () => {
    const mockProfile: UserProfile = {
      id: '1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      gender: 'male',
      dateOfBirth: '1990-01-01',
      heightCm: 180,
      weightKg: 75,
      activityLevel: 'moderate',
      fitnessGoal: 'muscle_gain',
      preferredWorkoutTime: '18:00',
      workoutFrequency: 4,
      themePreference: 'male',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    };

    useUserStore.getState().setProfile(mockProfile);
    expect(useUserStore.getState().profile).toEqual(mockProfile);
  });

  it('should update onboarding step', () => {
    useUserStore.getState().setOnboardingStep(2);
    expect(useUserStore.getState().onboarding.step).toBe(2);
  });
});
```

---

## 📱 Build and Run

### Development

```bash
# Start Metro bundler
npx react-native start

# Run on iOS
npx react-native run-ios

# Run on Android
npx react-native run-android
```

### Testing

```bash
# Run unit tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run E2E tests (after setting up Detox)
npm run e2e:ios
npm run e2e:android
```

### Build for Production

```bash
# iOS
cd ios && xcodebuild -workspace MyFitnessCoach.xcworkspace -scheme MyFitnessCoach -configuration Release -destination generic/platform=iOS -archivePath MyFitnessCoach.xcarchive archive

# Android
cd android && ./gradlew assembleRelease
```

---

## 🔧 Performance Optimization

### Image Optimization
- Use WebP format for images when possible
- Implement lazy loading for exercise videos
- Use React Native Fast Image for better caching

### Memory Management
- Clear image cache when memory is low
- Use FlatList for large data sets
- Implement proper cleanup in useEffect hooks

### Bundle Size Optimization
- Use Hermes JavaScript engine
- Enable ProGuard for Android
- Remove unused dependencies

---

## 📋 Checklist

### Phase 1 MVP Features
- [ ] User onboarding flow
- [ ] Profile setup with gender-specific themes
- [ ] Static workout plans display
- [ ] Exercise library with instructions
- [ ] Static meal plans
- [ ] Basic progress tracking
- [ ] Local data storage
- [ ] Offline functionality
- [ ] Gender-specific UI themes
- [ ] Form validation
- [ ] Error handling
- [ ] Loading states
- [ ] Unit tests
- [ ] Integration tests
- [ ] Performance optimization
- [ ] App store preparation

### Quality Assurance
- [ ] Cross-platform testing (iOS/Android)
- [ ] Different screen sizes testing
- [ ] Performance testing
- [ ] Memory leak testing
- [ ] Accessibility testing
- [ ] User experience testing

---

## 🚀 Next Steps (Phase 2)

After completing Phase 1, the next phase will include:
- Backend API integration
- User authentication
- Cloud data synchronization
- AI-powered personalization
- Social features
- Advanced analytics
- Push notifications
- In-app purchases

---

## 📞 Support

For implementation questions or issues:
1. Check the React Native documentation
2. Review the project's GitHub issues
3. Consult the team's technical lead
4. Use the project's Slack channel for quick questions

---

*This implementation guide provides a solid foundation for building the Phase 1 MVP of the My Fitness Coach app. Follow the steps sequentially and test thoroughly at each stage.*