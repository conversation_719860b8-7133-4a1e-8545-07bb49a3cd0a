var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=createIconButtonComponent;var _extends2=_interopRequireDefault(require("@babel/runtime/helpers/extends"));var _objectWithoutProperties2=_interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));var _classCallCheck2=_interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));var _createClass2=_interopRequireDefault(require("@babel/runtime/helpers/createClass"));var _possibleConstructorReturn2=_interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));var _getPrototypeOf2=_interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));var _inherits2=_interopRequireDefault(require("@babel/runtime/helpers/inherits"));var _react=_interopRequireWildcard(require("react"));var _propTypes=_interopRequireDefault(require("prop-types"));var _reactNative=require("react-native");var _objectUtils=require("./object-utils");var _jsxFileName="/Users/<USER>/Code/react-native-vector-icons/lib/icon-button.js";var _excluded=["style","iconStyle","children"];function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap(),t=new WeakMap();return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?t:r;})(e);}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u];}return n.default=e,t&&t.set(e,n),n;}function _callSuper(t,o,e){return o=(0,_getPrototypeOf2.default)(o),(0,_possibleConstructorReturn2.default)(t,_isNativeReflectConstruct()?Reflect.construct(o,e||[],(0,_getPrototypeOf2.default)(t).constructor):o.apply(t,e));}function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}));}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t;})();}var styles=_reactNative.StyleSheet.create({container:{flexDirection:'row',justifyContent:'flex-start',alignItems:'center',padding:8},touchable:{overflow:'hidden'},icon:{marginRight:10},text:{fontWeight:'600',backgroundColor:'transparent'}});var IOS7_BLUE='#007AFF';var TEXT_PROP_NAMES=['ellipsizeMode','numberOfLines','textBreakStrategy','selectable','suppressHighlighting','allowFontScaling','adjustsFontSizeToFit','minimumFontScale'];var TOUCHABLE_PROP_NAMES=['accessible','accessibilityLabel','accessibilityHint','accessibilityComponentType','accessibilityRole','accessibilityStates','accessibilityTraits','onFocus','onBlur','disabled','onPress','onPressIn','onPressOut','onLayout','onLongPress','nativeID','testID','delayPressIn','delayPressOut','delayLongPress','activeOpacity','underlayColor','selectionColor','onShowUnderlay','onHideUnderlay','hasTVPreferredFocus','tvParallaxProperties'];function createIconButtonComponent(Icon){var _IconButton;return _IconButton=function(_PureComponent){function IconButton(){(0,_classCallCheck2.default)(this,IconButton);return _callSuper(this,IconButton,arguments);}(0,_inherits2.default)(IconButton,_PureComponent);return(0,_createClass2.default)(IconButton,[{key:"render",value:function render(){var _this$props=this.props,style=_this$props.style,iconStyle=_this$props.iconStyle,children=_this$props.children,restProps=(0,_objectWithoutProperties2.default)(_this$props,_excluded);var iconProps=(0,_objectUtils.pick)(restProps,TEXT_PROP_NAMES,'style','name','size','color');var touchableProps=(0,_objectUtils.pick)(restProps,TOUCHABLE_PROP_NAMES);var props=(0,_objectUtils.omit)(restProps,Object.keys(iconProps),Object.keys(touchableProps),'iconStyle','borderRadius','backgroundColor');iconProps.style=iconStyle?[styles.icon,iconStyle]:styles.icon;var colorStyle=(0,_objectUtils.pick)(this.props,'color');var blockStyle=(0,_objectUtils.pick)(this.props,'backgroundColor','borderRadius');return _react.default.createElement(_reactNative.TouchableHighlight,(0,_extends2.default)({style:[styles.touchable,blockStyle]},touchableProps,{__self:this,__source:{fileName:_jsxFileName,lineNumber:119,columnNumber:9}}),_react.default.createElement(_reactNative.View,(0,_extends2.default)({style:[styles.container,blockStyle,style]},props,{__self:this,__source:{fileName:_jsxFileName,lineNumber:123,columnNumber:11}}),_react.default.createElement(Icon,(0,_extends2.default)({},iconProps,{__self:this,__source:{fileName:_jsxFileName,lineNumber:124,columnNumber:13}})),typeof children==='string'?_react.default.createElement(_reactNative.Text,{style:[styles.text,colorStyle],selectable:false,__self:this,__source:{fileName:_jsxFileName,lineNumber:126,columnNumber:15}},children):children));}}]);}(_react.PureComponent),_IconButton.propTypes={backgroundColor:_propTypes.default.oneOfType([_propTypes.default.string,_propTypes.default.number]),borderRadius:_propTypes.default.number,color:_propTypes.default.any,size:_propTypes.default.number,iconStyle:_propTypes.default.any,style:_propTypes.default.any,children:_propTypes.default.node},_IconButton.defaultProps={backgroundColor:IOS7_BLUE,borderRadius:5,color:'white',size:20},_IconButton;}