{"version": 3, "names": ["ERROR", "chalk", "bold", "startServerInNewWindow", "port", "projectRoot", "reactNativePath", "terminal", "logger", "error", "isWindows", "test", "process", "platform", "scriptFile", "packagerEnvFilename", "packagerEnvFileExportContent", "generatedPath", "findPackageDependencyDir", "startDir", "newPath", "path", "join", "fs", "mkdirSync", "recursive", "mode", "cliPluginMetroPath", "dirname", "require", "resolve", "packagerEnvFile", "launchPackagerScript", "procConfig", "cwd", "writeFileSync", "encoding", "flag", "copyFileSync", "execa", "sync", "detached", "stdio"], "sources": ["../src/startServerInNewWindow.ts"], "sourcesContent": ["import path from 'path';\nimport fs from 'fs';\nimport execa from 'execa';\nimport logger from './logger';\nimport chalk from 'chalk';\nimport {findPackageDependencyDir} from './findPackageDependencyDir';\n\nconst ERROR = `a dev server manually by running ${chalk.bold(\n  'npm start',\n)} or ${chalk.bold('yarn start')} in other terminal window.`;\n\nfunction startServerInNewWindow(\n  port: number,\n  projectRoot: string,\n  reactNativePath: string,\n  terminal?: string,\n) {\n  if (!terminal) {\n    logger.error(\n      'Cannot start server in new windows because no terminal app was specified, use --terminal to specify, or start ' +\n        ERROR,\n    );\n    return;\n  }\n\n  /**\n   * Set up OS-specific filenames and commands\n   */\n  const isWindows = /^win/.test(process.platform);\n  const scriptFile = isWindows\n    ? 'launchPackager.bat'\n    : 'launchPackager.command';\n  const packagerEnvFilename = isWindows ? '.packager.bat' : '.packager.env';\n  const packagerEnvFileExportContent = isWindows\n    ? `set RCT_METRO_PORT=${port}\\nset PROJECT_ROOT=${projectRoot}\\nset REACT_NATIVE_PATH=${reactNativePath}`\n    : `export RCT_METRO_PORT=${port}\\nexport PROJECT_ROOT=\"${projectRoot}\"\\nexport REACT_NATIVE_PATH=\"${reactNativePath}\"`;\n  let generatedPath = findPackageDependencyDir('.generated', {\n    startDir: projectRoot,\n  });\n\n  if (!generatedPath) {\n    const newPath = path.join(projectRoot, 'node_modules', '.generated');\n    fs.mkdirSync(newPath, {recursive: true, mode: 0o755});\n    generatedPath = newPath;\n  }\n\n  const cliPluginMetroPath = path.join(\n    path.dirname(\n      require.resolve('@react-native-community/cli-tools/package.json'),\n    ),\n    'build',\n  );\n\n  /**\n   * Set up the `.packager.(env|bat)` file to ensure the packager starts on the right port and in right directory.\n   */\n  const packagerEnvFile = path.join(generatedPath, `${packagerEnvFilename}`);\n\n  /**\n   * Set up the `launchPackager.(command|bat)` file.\n   * It lives next to `.packager.(bat|env)`\n   */\n  const launchPackagerScript = path.join(generatedPath, scriptFile);\n  const procConfig: execa.SyncOptions = {cwd: path.dirname(packagerEnvFile)};\n\n  /**\n   * Ensure we overwrite file by passing the `w` flag\n   */\n  fs.writeFileSync(packagerEnvFile, packagerEnvFileExportContent, {\n    encoding: 'utf8',\n    flag: 'w',\n  });\n\n  /**\n   * Copy files into `node_modules/.generated`.\n   */\n\n  try {\n    if (isWindows) {\n      fs.copyFileSync(\n        path.join(cliPluginMetroPath, 'launchPackager.bat'),\n        path.join(generatedPath, 'launchPackager.bat'),\n      );\n    } else {\n      fs.copyFileSync(\n        path.join(cliPluginMetroPath, 'launchPackager.command'),\n        path.join(generatedPath, 'launchPackager.command'),\n      );\n    }\n  } catch (error) {\n    logger.error(\n      `Couldn't copy the script for running bundler. Please check if the \"${scriptFile}\" file exists in the \"node_modules/@react-native-community/cli-tools\" folder, or start ` +\n        ERROR,\n      error as any,\n    );\n    return;\n  }\n\n  if (process.platform === 'darwin') {\n    try {\n      return execa.sync(\n        'open',\n        ['-na', terminal, launchPackagerScript],\n        procConfig,\n      );\n    } catch (error) {\n      return execa.sync('open', [launchPackagerScript], procConfig);\n    }\n  }\n  if (process.platform === 'linux') {\n    try {\n      return execa.sync(terminal, ['-e', `sh ${launchPackagerScript}`], {\n        ...procConfig,\n        detached: true,\n      });\n    } catch (error) {\n      // By default, the child shell process will be attached to the parent\n      return execa.sync('sh', [launchPackagerScript], procConfig);\n    }\n  }\n  if (isWindows) {\n    // Awaiting this causes the CLI to hang indefinitely, so this must execute without await.\n    return execa(terminal, ['/C', launchPackagerScript], {\n      ...procConfig,\n      detached: true,\n      stdio: 'ignore',\n    });\n  }\n\n  logger.error(\n    `Cannot start the packager. Unknown platform ${process.platform}. Try starting ` +\n      ERROR,\n  );\n  return;\n}\n\nexport default startServerInNewWindow;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAAoE;AAEpE,MAAMA,KAAK,GAAI,oCAAmCC,gBAAK,CAACC,IAAI,CAC1D,WAAW,CACX,OAAMD,gBAAK,CAACC,IAAI,CAAC,YAAY,CAAE,4BAA2B;AAE5D,SAASC,sBAAsB,CAC7BC,IAAY,EACZC,WAAmB,EACnBC,eAAuB,EACvBC,QAAiB,EACjB;EACA,IAAI,CAACA,QAAQ,EAAE;IACbC,eAAM,CAACC,KAAK,CACV,gHAAgH,GAC9GT,KAAK,CACR;IACD;EACF;;EAEA;AACF;AACA;EACE,MAAMU,SAAS,GAAG,MAAM,CAACC,IAAI,CAACC,OAAO,CAACC,QAAQ,CAAC;EAC/C,MAAMC,UAAU,GAAGJ,SAAS,GACxB,oBAAoB,GACpB,wBAAwB;EAC5B,MAAMK,mBAAmB,GAAGL,SAAS,GAAG,eAAe,GAAG,eAAe;EACzE,MAAMM,4BAA4B,GAAGN,SAAS,GACzC,sBAAqBN,IAAK,sBAAqBC,WAAY,2BAA0BC,eAAgB,EAAC,GACtG,yBAAwBF,IAAK,0BAAyBC,WAAY,gCAA+BC,eAAgB,GAAE;EACxH,IAAIW,aAAa,GAAG,IAAAC,kDAAwB,EAAC,YAAY,EAAE;IACzDC,QAAQ,EAAEd;EACZ,CAAC,CAAC;EAEF,IAAI,CAACY,aAAa,EAAE;IAClB,MAAMG,OAAO,GAAGC,eAAI,CAACC,IAAI,CAACjB,WAAW,EAAE,cAAc,EAAE,YAAY,CAAC;IACpEkB,aAAE,CAACC,SAAS,CAACJ,OAAO,EAAE;MAACK,SAAS,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAK,CAAC,CAAC;IACrDT,aAAa,GAAGG,OAAO;EACzB;EAEA,MAAMO,kBAAkB,GAAGN,eAAI,CAACC,IAAI,CAClCD,eAAI,CAACO,OAAO,CACVC,OAAO,CAACC,OAAO,CAAC,gDAAgD,CAAC,CAClE,EACD,OAAO,CACR;;EAED;AACF;AACA;EACE,MAAMC,eAAe,GAAGV,eAAI,CAACC,IAAI,CAACL,aAAa,EAAG,GAAEF,mBAAoB,EAAC,CAAC;;EAE1E;AACF;AACA;AACA;EACE,MAAMiB,oBAAoB,GAAGX,eAAI,CAACC,IAAI,CAACL,aAAa,EAAEH,UAAU,CAAC;EACjE,MAAMmB,UAA6B,GAAG;IAACC,GAAG,EAAEb,eAAI,CAACO,OAAO,CAACG,eAAe;EAAC,CAAC;;EAE1E;AACF;AACA;EACER,aAAE,CAACY,aAAa,CAACJ,eAAe,EAAEf,4BAA4B,EAAE;IAC9DoB,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;AACF;AACA;;EAEE,IAAI;IACF,IAAI3B,SAAS,EAAE;MACba,aAAE,CAACe,YAAY,CACbjB,eAAI,CAACC,IAAI,CAACK,kBAAkB,EAAE,oBAAoB,CAAC,EACnDN,eAAI,CAACC,IAAI,CAACL,aAAa,EAAE,oBAAoB,CAAC,CAC/C;IACH,CAAC,MAAM;MACLM,aAAE,CAACe,YAAY,CACbjB,eAAI,CAACC,IAAI,CAACK,kBAAkB,EAAE,wBAAwB,CAAC,EACvDN,eAAI,CAACC,IAAI,CAACL,aAAa,EAAE,wBAAwB,CAAC,CACnD;IACH;EACF,CAAC,CAAC,OAAOR,KAAK,EAAE;IACdD,eAAM,CAACC,KAAK,CACT,sEAAqEK,UAAW,yFAAwF,GACvKd,KAAK,EACPS,KAAK,CACN;IACD;EACF;EAEA,IAAIG,OAAO,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACjC,IAAI;MACF,OAAO0B,gBAAK,CAACC,IAAI,CACf,MAAM,EACN,CAAC,KAAK,EAAEjC,QAAQ,EAAEyB,oBAAoB,CAAC,EACvCC,UAAU,CACX;IACH,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACd,OAAO8B,gBAAK,CAACC,IAAI,CAAC,MAAM,EAAE,CAACR,oBAAoB,CAAC,EAAEC,UAAU,CAAC;IAC/D;EACF;EACA,IAAIrB,OAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;IAChC,IAAI;MACF,OAAO0B,gBAAK,CAACC,IAAI,CAACjC,QAAQ,EAAE,CAAC,IAAI,EAAG,MAAKyB,oBAAqB,EAAC,CAAC,EAAE;QAChE,GAAGC,UAAU;QACbQ,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACd;MACA,OAAO8B,gBAAK,CAACC,IAAI,CAAC,IAAI,EAAE,CAACR,oBAAoB,CAAC,EAAEC,UAAU,CAAC;IAC7D;EACF;EACA,IAAIvB,SAAS,EAAE;IACb;IACA,OAAO,IAAA6B,gBAAK,EAAChC,QAAQ,EAAE,CAAC,IAAI,EAAEyB,oBAAoB,CAAC,EAAE;MACnD,GAAGC,UAAU;MACbQ,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ;EAEAlC,eAAM,CAACC,KAAK,CACT,+CAA8CG,OAAO,CAACC,QAAS,iBAAgB,GAC9Eb,KAAK,CACR;EACD;AACF;AAAC,eAEcG,sBAAsB;AAAA"}