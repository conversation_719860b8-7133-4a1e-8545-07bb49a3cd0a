"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getLogOptions = void 0;
const getLogOptions = ({}) => [{
  name: '-i --interactive',
  description: 'Explicitly select simulator to tail logs from. By default it will tail logs from the first booted and available simulator.'
}];
exports.getLogOptions = getLogOptions;

//# sourceMappingURL=/Users/<USER>/Developer/cli/packages/cli-platform-apple/build/commands/logCommand/logOptions.js.map