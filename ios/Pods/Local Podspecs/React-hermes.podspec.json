{"name": "React-hermes", "version": "0.80.2", "summary": "Hermes engine for React Native", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.2"}, "public_header_files": "executor/HermesExecutorFactory.h", "source_files": ["executor/*.{cpp,h}", "inspector-modern/chrome/*.{cpp,h}", "executor/HermesExecutorFactory.h"], "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": ["\"${PODS_ROOT}/hermes-engine/destroot/include\"", "\"$(PODS_TARGET_SRCROOT)/..\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectorcdp/jsinspector_moderncdp.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing/jsinspector_moderntracing.framework/Headers\"", "$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"], "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "DEFINES_MODULE": "YES"}, "header_dir": "reacthermes", "dependencies": {"React-cxxreact": ["0.80.2"], "React-jsiexecutor": ["0.80.2"], "React-jsinspector": [], "React-jsinspectorcdp": [], "React-jsinspectortracing": [], "React-perflogger": ["0.80.2"], "hermes-engine": [], "React-jsi": [], "React-runtimeexecutor": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}}