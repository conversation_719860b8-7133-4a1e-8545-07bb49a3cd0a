{"name": "React-RuntimeApple", "version": "0.80.2", "summary": "The React Native Runtime.", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.2"}, "source_files": "ReactCommon/*.{mm,h}", "header_dir": "ReactCommon", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": ["$(PODS_ROOT)/Headers/Private/React-Core", "$(PODS_TARGET_SRCROOT)/../../../..", "$(PODS_TARGET_SRCROOT)/../../../../..", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling/JSITooling.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec/React_RCTFBReactNativeSpec.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes/React_RuntimeHermes.framework/Headers\"", "$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"], "USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "GCC_WARN_PEDANTIC": "YES"}, "dependencies": {"React-jsiexecutor": [], "React-cxxreact": [], "React-callinvoker": [], "React-runtimeexecutor": [], "React-runtimescheduler": [], "React-utils": [], "React-jsi": [], "React-Core/Default": [], "React-CoreModules": [], "React-NativeModulesApple": [], "React-RCTFabric": [], "React-RuntimeCore": [], "React-Mapbuffer": [], "React-jserrorhandler": [], "React-jsinspector": [], "React-featureflags": [], "React-jsitooling": [], "React-RCTFBReactNativeSpec": [], "hermes-engine": [], "React-RuntimeHermes": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}, "exclude_files": "ReactCommon/RCTJscInstance.{mm,h}"}