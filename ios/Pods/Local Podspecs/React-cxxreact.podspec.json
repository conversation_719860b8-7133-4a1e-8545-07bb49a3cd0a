{"name": "React-cxxreact", "version": "0.80.2", "summary": "-", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.2"}, "source_files": "*.{cpp,h}", "exclude_files": "SampleCxxModule.*", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": ["\"$(PODS_CONFIGURATION_BUILD_DIR)/React-debug/React_debug.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-runtimeexecutor/React_runtimeexecutor.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectorcdp/jsinspector_moderncdp.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing/jsinspector_moderntracing.framework/Headers\"", "$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"], "CLANG_CXX_LANGUAGE_STANDARD": "c++20"}, "header_dir": "cxxreact", "dependencies": {"React-jsinspector": [], "React-jsinspectorcdp": [], "React-jsinspectortracing": [], "React-callinvoker": ["0.80.2"], "React-runtimeexecutor": ["0.80.2"], "React-perflogger": ["0.80.2"], "React-jsi": ["0.80.2"], "React-logger": ["0.80.2"], "React-debug": ["0.80.2"], "React-timing": ["0.80.2"], "hermes-engine": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}, "resource_bundles": {"React-cxxreact_privacy": "PrivacyInfo.xcprivacy"}}