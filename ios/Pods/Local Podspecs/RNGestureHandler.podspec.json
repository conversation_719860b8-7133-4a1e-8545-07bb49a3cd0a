{"name": "RNGestureHandler", "version": "2.27.2", "summary": "Declarative API exposing native platform touch and gesture system to React Native", "homepage": "https://github.com/software-mansion/react-native-gesture-handler", "license": "MIT", "authors": {"Krzysztof Magiera": "<EMAIL>"}, "source": {"git": "https://github.com/software-mansion/react-native-gesture-handler", "tag": "2.27.2"}, "source_files": "apple/**/*.{h,m,mm}", "requires_arc": true, "platforms": {"ios": "11.0", "tvos": "11.0", "osx": "10.15", "visionos": "1.0"}, "xcconfig": {"OTHER_CFLAGS": "$(inherited) "}, "compiler_flags": "-DRCT_NEW_ARCH_ENABLED=1", "dependencies": {"React-Core": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "React-jsi": [], "React-renderercss": [], "hermes-engine": [], "React-hermes": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}, "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 ", "OTHER_SWIFT_FLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED"}}