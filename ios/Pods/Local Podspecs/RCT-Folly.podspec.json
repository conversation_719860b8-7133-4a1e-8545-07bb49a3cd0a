{"name": "RCT-Folly", "version": "2024.11.18.00", "license": {"type": "Apache License, Version 2.0"}, "homepage": "https://github.com/facebook/folly", "summary": "An open-source C++ library developed and used at Facebook.", "authors": "Facebook", "source": {"git": "https://github.com/facebook/folly.git", "tag": "v2024.11.18.00"}, "module_name": "folly", "prepare_command": "echo '#pragma once\n\n#define FOLLY_MOBILE 1\n#define FOLLY_USE_LIBCPP 1\n#define FOLLY_HAVE_PTHREAD 1\n#define FOLLY_CFG_NO_COROUTINES 1\n#define FOLLY_HAVE_CLOCK_GETTIME 1\n\n#pragma clang diagnostic ignored \"-Wcomma\"' > folly/folly-config.h", "header_mappings_dir": ".", "dependencies": {"boost": [], "DoubleConversion": [], "glog": [], "fast_float": ["8.0.0"], "fmt": ["11.0.2"]}, "compiler_flags": "-Wno-documentation -faligned-new", "preserve_paths": "folly/*.h", "libraries": "c++abi", "source_files": ["folly/String.cpp", "folly/Conv.cpp", "folly/Demangle.cpp", "folly/FileUtil.cpp", "folly/Format.cpp", "folly/lang/SafeAssert.cpp", "folly/lang/ToAscii.cpp", "folly/ScopeGuard.cpp", "folly/Unicode.cpp", "folly/json/dynamic.cpp", "folly/json/json.cpp", "folly/json/json_pointer.cpp", "folly/container/detail/F14Table.cpp", "folly/detail/Demangle.cpp", "folly/detail/FileUtilDetail.cpp", "folly/detail/SplitStringSimd.cpp", "folly/detail/StaticSingletonManager.cpp", "folly/detail/UniqueInstance.cpp", "folly/hash/SpookyHashV2.cpp", "folly/lang/CString.cpp", "folly/lang/Exception.cpp", "folly/memory/ReentrantAllocator.cpp", "folly/memory/detail/MallocImpl.cpp", "folly/net/NetOps.cpp", "folly/portability/SysUio.cpp", "folly/synchronization/SanitizeThread.cpp", "folly/system/AtFork.cpp", "folly/system/ThreadId.cpp", "folly/*.h", "folly/algorithm/simd/*.h", "folly/algorithm/simd/detail/*.h", "folly/chrono/*.h", "folly/container/*.h", "folly/container/detail/*.h", "folly/detail/*.h", "folly/functional/*.h", "folly/hash/*.h", "folly/json/*.h", "folly/lang/*.h", "folly/memory/*.h", "folly/memory/detail/*.h", "folly/net/*.h", "folly/net/detail/*.h", "folly/portability/*.h", "folly/system/*.h", "folly/*.h", "folly/algorithm/simd/*.h", "folly/algorithm/simd/detail/*.h", "folly/chrono/*.h", "folly/container/*.h", "folly/container/detail/*.h", "folly/detail/*.h", "folly/functional/*.h", "folly/hash/*.h", "folly/json/*.h", "folly/lang/*.h", "folly/memory/*.h", "folly/memory/detail/*.h", "folly/net/*.h", "folly/net/detail/*.h", "folly/portability/*.h", "folly/system/*.h", "c++abi"], "pod_target_xcconfig": {"USE_HEADERMAP": "NO", "DEFINES_MODULE": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)\" \"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/DoubleConversion\" \"$(PODS_ROOT)/fast_float/include\" \"$(PODS_ROOT)/fmt/include\"", "OTHER_LDFLAGS": "\"-Wl,-U,_jump_fcontext\" \"-Wl,-U,_make_fcontext\"", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES"}, "user_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\""}, "default_subspecs": "<PERSON><PERSON><PERSON>", "resource_bundles": {"RCT-Folly_privacy": "RCT-Folly/PrivacyInfo.xcprivacy"}, "platforms": {"ios": "15.1"}, "subspecs": [{"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "source_files": ["folly/SharedMutex.cpp", "folly/concurrency/CacheLocality.cpp", "folly/detail/Futex.cpp", "folly/synchronization/ParkingLot.cpp", "folly/concurrency/CacheLocality.h", "folly/synchronization/*.h", "folly/system/ThreadId.h"], "preserve_paths": ["folly/concurrency/CacheLocality.h", "folly/synchronization/*.h", "folly/system/ThreadId.h"]}]}