{"name": "React-graphics", "version": "0.80.2", "summary": "<PERSON><PERSON><PERSON> for React Native.", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.2"}, "source_files": ["*.{m,mm,cpp,h}", "platform/ios/**/*.{m,mm,cpp,h}"], "header_dir": "react/renderer/graphics", "frameworks": "UIKit", "pod_target_xcconfig": {"USE_HEADERMAP": "NO", "HEADER_SEARCH_PATHS": ["\"$(PODS_TARGET_SRCROOT)/../../../\"", "$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"], "DEFINES_MODULE": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20"}, "dependencies": {"React-jsi": [], "React-jsiexecutor": [], "React-utils": [], "hermes-engine": [], "React-hermes": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}}