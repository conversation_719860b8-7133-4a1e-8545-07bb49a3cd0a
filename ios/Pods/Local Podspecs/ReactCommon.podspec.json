{"name": "ReactCommon", "module_name": "ReactCommon", "version": "0.80.2", "summary": "-", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.2"}, "header_dir": "ReactCommon", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": ["\"$(PODS_ROOT)/Headers/Private/React-Core\"", "$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"], "USE_HEADERMAP": "YES", "DEFINES_MODULE": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "GCC_WARN_PEDANTIC": "YES"}, "dependencies": {"glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}, "subspecs": [{"name": "turbomodule", "dependencies": {"React-callinvoker": ["0.80.2"], "React-perflogger": ["0.80.2"], "React-cxxreact": ["0.80.2"], "React-jsi": ["0.80.2"], "React-logger": ["0.80.2"], "hermes-engine": []}, "subspecs": [{"name": "bridging", "dependencies": {"React-jsi": ["0.80.2"], "hermes-engine": []}, "source_files": "react/bridging/**/*.{cpp,h}", "exclude_files": "react/bridging/tests", "header_dir": "react/bridging", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/ReactCommon\""}}, {"name": "core", "source_files": "react/nativemodule/core/ReactCommon/**/*.{cpp,h}", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/ReactCommon\" \"$(PODS_CONFIGURATION_BUILD_DIR)/React-debug/React_debug.framework/Headers\" \"$(PODS_CONFIGURATION_BUILD_DIR)/React-debug/React_featureflags.framework/Headers\" \"$(PODS_CONFIGURATION_BUILD_DIR)/React-utils/React_utils.framework/Headers\""}, "dependencies": {"React-debug": ["0.80.2"], "React-featureflags": ["0.80.2"], "React-utils": ["0.80.2"]}}]}]}