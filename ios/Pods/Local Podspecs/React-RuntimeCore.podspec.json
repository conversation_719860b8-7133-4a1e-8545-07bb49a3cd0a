{"name": "React-RuntimeCore", "version": "0.80.2", "summary": "The React Native Runtime.", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.2"}, "source_files": ["*.{cpp,h}", "nativeviewconfig/*.{cpp,h}"], "exclude_files": ["iostests/*", "tests/**/*.{cpp,h}"], "header_dir": "react/runtime", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/Headers/Private/React-Core\" \"${PODS_TARGET_SRCROOT}/../..\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers\" $(PODS_ROOT)/glog $(PODS_ROOT)/boost $(PODS_ROOT)/DoubleConversion $(PODS_ROOT)/fast_float/include $(PODS_ROOT)/fmt/include $(PODS_ROOT)/SocketRocket $(PODS_ROOT)/RCT-Folly \"${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling/JSITooling.framework/Headers\"", "USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "GCC_WARN_PEDANTIC": "YES"}, "dependencies": {"React-jsiexecutor": [], "React-cxxreact": [], "React-runtimeexecutor": [], "React-jsi": [], "React-jserrorhandler": [], "React-performancetimeline": [], "React-runtimescheduler": [], "React-utils": [], "React-featureflags": [], "React-Fabric": [], "hermes-engine": [], "React-hermes": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": [], "React-jsinspector": [], "React-jsitooling": []}}