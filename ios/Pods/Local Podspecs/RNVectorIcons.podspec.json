{"name": "RNVectorIcons", "version": "10.3.0", "summary": "Customizable Icons for React Native with support for NavBar/TabBar, image source and full styling.", "description": "Customizable Icons for React Native with support for NavBar/TabBar, image source and full styling.", "homepage": "https://github.com/oblador/react-native-vector-icons", "license": "MIT", "authors": {"Joel Arvidsson": "<EMAIL>"}, "platforms": {"ios": "12.0", "tvos": "9.0", "visionos": "1.0"}, "source": {"git": "git://github.com/oblador/react-native-vector-icons.git", "tag": "v10.3.0"}, "source_files": "RNVectorIconsManager/**/*.{h,m,mm,swift}", "resources": "Fonts/*.ttf", "preserve_paths": "**/*.js", "compiler_flags": "-DRCT_NEW_ARCH_ENABLED=1", "dependencies": {"React-Core": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "React-jsi": [], "React-renderercss": [], "hermes-engine": [], "React-hermes": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}, "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 ", "OTHER_SWIFT_FLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED"}}