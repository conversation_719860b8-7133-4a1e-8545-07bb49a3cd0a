{"name": "React-microtasksnativemodule", "version": "0.80.2", "summary": "React Native microtasks native module", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.2"}, "source_files": "*.{cpp,h}", "header_dir": "react/nativemodule/microtasks", "pod_target_xcconfig": {"CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CFLAGS": "$(inherited)", "HEADER_SEARCH_PATHS": "$(PODS_ROOT)/glog $(PODS_ROOT)/boost $(PODS_ROOT)/DoubleConversion $(PODS_ROOT)/fast_float/include $(PODS_ROOT)/fmt/include $(PODS_ROOT)/SocketRocket $(PODS_ROOT)/RCT-Folly \"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec/React_RCTFBReactNativeSpec.framework/Headers\"", "DEFINES_MODULE": "YES"}, "dependencies": {"React-jsi": [], "React-jsiexecutor": [], "hermes-engine": [], "React-hermes": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": [], "ReactCommon/turbomodule/core": [], "React-RCTFBReactNativeSpec": []}}