{"name": "React-jsi", "version": "0.80.2", "summary": "JavaScript Interface layer for React Native", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.2"}, "header_dir": "jsi", "pod_target_xcconfig": {"CLANG_CXX_LANGUAGE_STANDARD": "c++20", "DEFINES_MODULE": "YES", "HEADER_SEARCH_PATHS": ["$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"]}, "source_files": "**/*.{cpp,h}", "dependencies": {"hermes-engine": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}, "exclude_files": ["jsi/jsilib-posix.cpp", "jsi/jsilib-windows.cpp", "**/test/*", "jsi/jsi.cpp"]}