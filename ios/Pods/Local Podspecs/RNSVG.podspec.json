{"name": "RNSVG", "version": "15.12.1", "summary": "SVG library for react-native", "license": "MIT", "homepage": "https://github.com/react-native-community/react-native-svg", "authors": "<PERSON><PERSON><PERSON><PERSON>", "source": {"git": "https://github.com/react-native-community/react-native-svg.git", "tag": "v15.12.1"}, "source_files": "apple/**/*.{h,m,mm}", "ios": {"exclude_files": "**/*.macos.{h,m,mm}", "resource_bundles": {"RNSVGFilters": ["apple/**/*.iphoneos.metallib"]}}, "tvos": {"exclude_files": "**/*.macos.{h,m,mm}", "resource_bundles": {"RNSVGFilters": ["apple/**/*.appletvos.metallib"]}}, "visionos": {"exclude_files": "**/*.macos.{h,m,mm}", "resource_bundles": {"RNSVGFilters": ["apple/**/*.xros.metallib"]}}, "osx": {"exclude_files": "**/*.ios.{h,m,mm}", "resource_bundles": {"RNSVGFilters": ["apple/**/*.macosx.metallib"]}}, "requires_arc": true, "platforms": {"osx": "10.14", "ios": "12.4", "tvos": "12.4", "visionos": "1.0"}, "xcconfig": {"OTHER_CFLAGS": "$(inherited) -DREACT_NATIVE_MINOR_VERSION=80"}, "compiler_flags": "-DRCT_NEW_ARCH_ENABLED=1", "dependencies": {"React-Core": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "React-jsi": [], "React-renderercss": [], "hermes-engine": [], "React-hermes": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}, "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 ", "OTHER_SWIFT_FLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED"}, "subspecs": [{"name": "common", "source_files": "common/cpp/**/*.{cpp,h}", "header_dir": "rnsvg", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/common/cpp\""}}]}