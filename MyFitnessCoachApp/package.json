{"name": "MyFitnessCoachApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "web": "webpack serve --config webpack.config.js", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.0", "@react-native/new-app-screen": "0.80.2", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^6.1.0", "@react-navigation/stack": "^6.3.0", "react": "19.1.0", "react-dom": "^19.1.1", "react-hook-form": "^7.45.0", "react-native": "0.80.2", "react-native-gesture-handler": "^2.12.0", "react-native-mmkv": "^2.10.0", "react-native-safe-area-context": "^4.10.5", "react-native-screens": "^3.22.0", "react-native-svg": "^13.10.0", "react-native-web": "^0.21.0", "yup": "^1.2.0", "zustand": "^4.4.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.1.1", "@react-native-community/cli-platform-android": "19.1.1", "@react-native-community/cli-platform-ios": "19.1.1", "@react-native/babel-preset": "0.80.2", "@react-native/eslint-config": "0.80.2", "@react-native/metro-config": "0.80.2", "@react-native/typescript-config": "0.80.2", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "babel-loader": "^10.0.0", "eslint": "^8.19.0", "html-webpack-plugin": "^5.6.3", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "5.0.4", "webpack": "^5.101.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "engines": {"node": ">=18"}}