import { useState, useCallback, useRef } from 'react';
import * as Yup from 'yup';

export interface FormField<T = any> {
  value: T;
  error?: string;
  touched: boolean;
  dirty: boolean;
}

export interface FormState<T extends Record<string, any>> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  dirty: Partial<Record<keyof T, boolean>>;
  isValid: boolean;
  isSubmitting: boolean;
  submitCount: number;
}

export interface UseFormOptions<T extends Record<string, any>> {
  initialValues: T;
  validationSchema?: Yup.ObjectSchema<any>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  onSubmit?: (values: T) => Promise<void> | void;
}

export interface UseFormReturn<T extends Record<string, any>> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  dirty: Partial<Record<keyof T, boolean>>;
  isValid: boolean;
  isSubmitting: boolean;
  submitCount: number;
  
  // Field helpers
  getFieldProps: (name: keyof T) => {
    value: T[keyof T];
    onChangeText: (value: string) => void;
    onBlur: () => void;
    error?: string;
    touched: boolean;
  };
  
  // Form actions
  setFieldValue: (name: keyof T, value: T[keyof T]) => void;
  setFieldError: (name: keyof T, error: string) => void;
  setFieldTouched: (name: keyof T, touched?: boolean) => void;
  setValues: (values: Partial<T>) => void;
  setErrors: (errors: Partial<Record<keyof T, string>>) => void;
  setTouched: (touched: Partial<Record<keyof T, boolean>>) => void;
  
  // Form validation
  validateField: (name: keyof T) => Promise<string | undefined>;
  validateForm: () => Promise<Partial<Record<keyof T, string>>>;
  
  // Form submission
  handleSubmit: () => Promise<void>;
  
  // Form reset
  resetForm: (values?: Partial<T>) => void;
  
  // Utility
  isFieldValid: (name: keyof T) => boolean;
  isFieldInvalid: (name: keyof T) => boolean;
}

/**
 * Custom hook for form state management with validation support.
 * Provides comprehensive form handling including validation, error management,
 * and submission handling.
 */
export function useForm<T extends Record<string, any>>({
  initialValues,
  validationSchema,
  validateOnChange = true,
  validateOnBlur = true,
  onSubmit,
}: UseFormOptions<T>): UseFormReturn<T> {
  const [values, setValuesState] = useState<T>(initialValues);
  const [errors, setErrorsState] = useState<Partial<Record<keyof T, string>>>({});
  const [touched, setTouchedState] = useState<Partial<Record<keyof T, boolean>>>({});
  const [dirty, setDirtyState] = useState<Partial<Record<keyof T, boolean>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitCount, setSubmitCount] = useState(0);
  
  const initialValuesRef = useRef(initialValues);

  // Validate a single field
  const validateField = useCallback(async (name: keyof T): Promise<string | undefined> => {
    if (!validationSchema) return undefined;

    try {
      await validationSchema.validateAt(name as string, values);
      return undefined;
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        return error.message;
      }
      return 'Validation error';
    }
  }, [validationSchema, values]);

  // Validate entire form
  const validateForm = useCallback(async (): Promise<Partial<Record<keyof T, string>>> => {
    if (!validationSchema) return {};

    try {
      await validationSchema.validate(values, { abortEarly: false });
      return {};
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        const validationErrors: Partial<Record<keyof T, string>> = {};
        error.inner.forEach((err) => {
          if (err.path) {
            validationErrors[err.path as keyof T] = err.message;
          }
        });
        return validationErrors;
      }
      return {};
    }
  }, [validationSchema, values]);

  // Set field value
  const setFieldValue = useCallback((name: keyof T, value: T[keyof T]) => {
    setValuesState(prev => ({ ...prev, [name]: value }));
    setDirtyState(prev => ({ ...prev, [name]: true }));

    // Validate on change if enabled
    if (validateOnChange) {
      validateField(name).then(error => {
        setErrorsState(prev => ({ ...prev, [name]: error }));
      });
    }
  }, [validateOnChange, validateField]);

  // Set field error
  const setFieldError = useCallback((name: keyof T, error: string) => {
    setErrorsState(prev => ({ ...prev, [name]: error }));
  }, []);

  // Set field touched
  const setFieldTouched = useCallback((name: keyof T, isTouched: boolean = true) => {
    setTouchedState(prev => ({ ...prev, [name]: isTouched }));

    // Validate on blur if enabled
    if (validateOnBlur && isTouched) {
      validateField(name).then(error => {
        setErrorsState(prev => ({ ...prev, [name]: error }));
      });
    }
  }, [validateOnBlur, validateField]);

  // Set multiple values
  const setValues = useCallback((newValues: Partial<T>) => {
    setValuesState(prev => ({ ...prev, ...newValues }));
    
    // Mark fields as dirty
    const dirtyFields: Partial<Record<keyof T, boolean>> = {};
    Object.keys(newValues).forEach(key => {
      dirtyFields[key as keyof T] = true;
    });
    setDirtyState(prev => ({ ...prev, ...dirtyFields }));
  }, []);

  // Set multiple errors
  const setErrors = useCallback((newErrors: Partial<Record<keyof T, string>>) => {
    setErrorsState(prev => ({ ...prev, ...newErrors }));
  }, []);

  // Set multiple touched fields
  const setTouched = useCallback((newTouched: Partial<Record<keyof T, boolean>>) => {
    setTouchedState(prev => ({ ...prev, ...newTouched }));
  }, []);

  // Get field props for easy integration with form inputs
  const getFieldProps = useCallback((name: keyof T) => {
    return {
      value: values[name],
      onChangeText: (value: string) => setFieldValue(name, value as T[keyof T]),
      onBlur: () => setFieldTouched(name, true),
      error: errors[name],
      touched: touched[name] || false,
    };
  }, [values, errors, touched, setFieldValue, setFieldTouched]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (!onSubmit) return;

    setIsSubmitting(true);
    setSubmitCount(prev => prev + 1);

    try {
      // Mark all fields as touched
      const allTouched: Partial<Record<keyof T, boolean>> = {};
      Object.keys(values).forEach(key => {
        allTouched[key as keyof T] = true;
      });
      setTouchedState(allTouched);

      // Validate form
      const formErrors = await validateForm();
      setErrorsState(formErrors);

      // Check if form is valid
      const hasErrors = Object.keys(formErrors).length > 0;
      if (hasErrors) {
        return;
      }

      // Submit form
      await onSubmit(values);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [onSubmit, values, validateForm]);

  // Reset form
  const resetForm = useCallback((newValues?: Partial<T>) => {
    const resetValues = newValues ? { ...initialValuesRef.current, ...newValues } : initialValuesRef.current;
    setValuesState(resetValues);
    setErrorsState({});
    setTouchedState({});
    setDirtyState({});
    setIsSubmitting(false);
    setSubmitCount(0);
  }, []);

  // Utility functions
  const isFieldValid = useCallback((name: keyof T) => {
    return !errors[name] && !!touched[name];
  }, [errors, touched]);

  const isFieldInvalid = useCallback((name: keyof T) => {
    return !!errors[name] && !!touched[name];
  }, [errors, touched]);

  // Calculate if form is valid
  const isValid = Object.keys(errors).length === 0;

  return {
    values,
    errors,
    touched,
    dirty,
    isValid,
    isSubmitting,
    submitCount,
    
    getFieldProps,
    
    setFieldValue,
    setFieldError,
    setFieldTouched,
    setValues,
    setErrors,
    setTouched,
    
    validateField,
    validateForm,
    
    handleSubmit,
    
    resetForm,
    
    isFieldValid,
    isFieldInvalid,
  };
}

/**
 * Simplified form hook for basic forms without complex validation
 */
export function useSimpleForm<T extends Record<string, any>>(initialValues: T) {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});

  const setValue = useCallback((name: keyof T, value: T[keyof T]) => {
    setValues(prev => ({ ...prev, [name]: value }));
    // Clear error when value changes
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  }, [errors]);

  const setError = useCallback((name: keyof T, error: string) => {
    setErrors(prev => ({ ...prev, [name]: error }));
  }, []);

  const reset = useCallback(() => {
    setValues(initialValues);
    setErrors({});
  }, [initialValues]);

  return {
    values,
    errors,
    setValue,
    setError,
    setValues,
    setErrors,
    reset,
  };
}