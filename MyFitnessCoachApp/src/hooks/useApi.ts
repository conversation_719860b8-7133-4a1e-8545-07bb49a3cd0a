import { useState, useCallback } from 'react';
import { useUserStore } from '../stores/userStore';
import { errorService } from '../services/errorService';

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  success: boolean;
}

export interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  requiresAuth?: boolean;
}

export interface UseApiReturn<T = any> {
  data: T | null;
  loading: boolean;
  error: string | null;
  execute: (url: string, options?: ApiOptions) => Promise<ApiResponse<T>>;
  reset: () => void;
}

/**
 * Custom hook for making API calls with automatic error handling,
 * loading states, and authentication token management.
 */
export function useApi<T = any>(): UseApiReturn<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { authToken, refreshAuthToken, logout } = useUserStore();

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  const execute = useCallback(async (
    url: string,
    options: ApiOptions = {}
  ): Promise<ApiResponse<T>> => {
    const {
      method = 'GET',
      headers = {},
      body,
      requiresAuth = true,
    } = options;

    setLoading(true);
    setError(null);

    try {
      // Prepare headers
      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        ...headers,
      };

      // Add auth token if required
      if (requiresAuth && authToken) {
        requestHeaders.Authorization = `Bearer ${authToken}`;
      }

      // Prepare request config
      const requestConfig: RequestInit = {
        method,
        headers: requestHeaders,
      };

      // Add body for non-GET requests
      if (body && method !== 'GET') {
        requestConfig.body = JSON.stringify(body);
      }

      // Make the request
      const response = await fetch(url, requestConfig);

      // Handle authentication errors
      if (response.status === 401 && requiresAuth) {
        try {
          // Try to refresh token
          await refreshAuthToken();
          // Retry the request with new token
          const retryHeaders = {
            ...requestHeaders,
            Authorization: `Bearer ${useUserStore.getState().authToken}`,
          };
          const retryResponse = await fetch(url, {
            ...requestConfig,
            headers: retryHeaders,
          });
          
          if (retryResponse.status === 401) {
            // If still unauthorized, logout user
            logout();
            throw new Error('Session expired. Please login again.');
          }
          
          // Use retry response for further processing
          const retryData = await retryResponse.json();
          setData(retryData);
          setLoading(false);
          return { data: retryData, success: true };
        } catch (refreshError) {
          logout();
          const errorMessage = 'Session expired. Please login again.';
          setError(errorMessage);
          setLoading(false);
          errorService.logCriticalError('Token refresh failed', refreshError as Error, { action: 'useApi.execute.refresh' });
          return { error: errorMessage, success: false };
        }
      }

      // Handle other HTTP errors
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.message || `HTTP ${response.status}: ${response.statusText}`;
        throw new Error(errorMessage);
      }

      // Parse successful response
      const responseData = await response.json();
      setData(responseData);
      setLoading(false);
      
      return { data: responseData, success: true };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      setLoading(false);
      
      // Log error for debugging
      errorService.logCriticalError('API request failed', err as Error, { action: 'useApi.execute' });
      
      return { error: errorMessage, success: false };
    }
  }, [authToken, refreshAuthToken, logout]);

  return {
    data,
    loading,
    error,
    execute,
    reset,
  };
}

/**
 * Specialized hook for GET requests
 */
export function useApiGet<T = any>(url: string, options?: Omit<ApiOptions, 'method'>) {
  const api = useApi<T>();
  
  const get = useCallback(() => {
    return api.execute(url, { ...options, method: 'GET' });
  }, [api, url, options]);

  return {
    ...api,
    get,
  };
}

/**
 * Specialized hook for POST requests
 */
export function useApiPost<T = any>() {
  const api = useApi<T>();
  
  const post = useCallback((url: string, body: any, options?: Omit<ApiOptions, 'method' | 'body'>) => {
    return api.execute(url, { ...options, method: 'POST', body });
  }, [api]);

  return {
    ...api,
    post,
  };
}

/**
 * Specialized hook for PUT requests
 */
export function useApiPut<T = any>() {
  const api = useApi<T>();
  
  const put = useCallback((url: string, body: any, options?: Omit<ApiOptions, 'method' | 'body'>) => {
    return api.execute(url, { ...options, method: 'PUT', body });
  }, [api]);

  return {
    ...api,
    put,
  };
}

/**
 * Specialized hook for DELETE requests
 */
export function useApiDelete<T = any>() {
  const api = useApi<T>();
  
  const del = useCallback((url: string, options?: Omit<ApiOptions, 'method'>) => {
    return api.execute(url, { ...options, method: 'DELETE' });
  }, [api]);

  return {
    ...api,
    delete: del,
  };
}