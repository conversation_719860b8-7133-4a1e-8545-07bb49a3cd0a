import { useMemo } from 'react';
import { useUserStore } from '../stores/userStore';
import { theme as baseTheme } from '../theme';

export type ThemeVariant = 'neutral' | 'masculine' | 'feminine';
export type ColorScheme = 'light' | 'dark';

export interface ThemeColors {
  primary: typeof baseTheme.colors.primary;
  secondary: typeof baseTheme.colors.secondary;
  accent: typeof baseTheme.colors.accent;
  neutral: typeof baseTheme.colors.neutral;
  success: string;
  warning: string;
  error: string;
  info: string;
  background: typeof baseTheme.colors.background;
  text: typeof baseTheme.colors.text;
  border: typeof baseTheme.colors.border;
}

export interface Theme {
  colors: ThemeColors;
  typography: typeof baseTheme.typography;
  spacing: typeof baseTheme.spacing;
  borderRadius: typeof baseTheme.borderRadius;
  shadows: typeof baseTheme.shadows;
  variant: ThemeVariant;
  colorScheme: ColorScheme;
}

// Gender-specific color variations
const themeVariants: Record<ThemeVariant, Partial<ThemeColors>> = {
  neutral: {
    // Use base theme colors
  },
  masculine: {
    primary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9', // Blue primary
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
    },
    accent: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e', // Green accent
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
    },
  },
  feminine: {
    primary: {
      50: '#fdf2f8',
      100: '#fce7f3',
      200: '#fbcfe8',
      300: '#f9a8d4',
      400: '#f472b6',
      500: '#ec4899', // Pink primary
      600: '#db2777',
      700: '#be185d',
      800: '#9d174d',
      900: '#831843',
    },
    accent: {
      50: '#fefce8',
      100: '#fef9c3',
      200: '#fef08a',
      300: '#fde047',
      400: '#facc15',
      500: '#eab308', // Yellow accent
      600: '#ca8a04',
      700: '#a16207',
      800: '#854d0e',
      900: '#713f12',
    },
  },
};

// Dark mode color adjustments
// const darkModeAdjustments = {
//   background: {
//     primary: '#0f172a',
//     secondary: '#1e293b',
//     tertiary: '#334155',
//   },
//   text: {
//     primary: '#f8fafc',
//     secondary: '#cbd5e1',
//     tertiary: '#94a3b8',
//     inverse: '#0f172a',
//   },
//   border: {
//     light: '#334155',
//     medium: '#475569',
//     dark: '#64748b',
//   },
// };

/**
 * Custom hook for accessing and managing theme based on user preferences.
 * Provides gender-specific theming and dark mode support.
 */
export function useTheme(): Theme {
  const { user } = useUserStore();
  
  const theme = useMemo(() => {
    // Determine theme variant based on user preferences or gender
    let variant: ThemeVariant = 'neutral';
    if (user?.preferences?.theme) {
      variant = user.preferences.theme as ThemeVariant;
    } else if (user?.gender) {
      // Map gender to theme variant
      variant = user.gender === 'male' ? 'masculine' : user.gender === 'female' ? 'feminine' : 'neutral';
    }
    
    // For now, we'll use light mode. Dark mode can be added later
    const colorScheme: ColorScheme = 'light';
    
    // Get variant-specific colors
    const variantColors = themeVariants[variant];
    
    // Merge base colors with variant colors
    const colors: ThemeColors = {
      ...baseTheme.colors,
      ...variantColors,
    };
    
    // Apply dark mode adjustments if needed (future feature)
    // if (colorScheme === 'dark') {
    //   Object.assign(colors, {
    //     background: darkModeAdjustments.background,
    //     text: darkModeAdjustments.text,
    //     border: darkModeAdjustments.border,
    //   });
    // }
    
    return {
      colors,
      typography: baseTheme.typography,
      spacing: baseTheme.spacing,
      borderRadius: baseTheme.borderRadius,
      shadows: baseTheme.shadows,
      variant,
      colorScheme,
    };
  }, [user?.preferences?.theme, user?.gender]);
  
  return theme;
}

/**
 * Hook for getting theme-aware styles
 */
export function useThemedStyles<T extends Record<string, any>>(
  styleFactory: (theme: Theme) => T
): T {
  const theme = useTheme();
  
  return useMemo(() => styleFactory(theme), [styleFactory, theme]);
}

/**
 * Hook for getting gender-specific content and styling
 */
export function useGenderTheming() {
  const { user } = useUserStore();
  const theme = useTheme();
  
  const genderInfo = useMemo(() => {
    const gender = user?.gender || 'male';
    const variant = theme.variant;
    
    return {
      gender,
      variant,
      isMasculine: variant === 'masculine',
      isFeminine: variant === 'feminine',
      isNeutral: variant === 'neutral',
      
      // Gender-specific content helpers
      getGenderedText: (maleText: string, femaleText: string, neutralText?: string) => {
        if (variant === 'masculine') return maleText;
        if (variant === 'feminine') return femaleText;
        return neutralText || maleText;
      },
      
      // Gender-specific color helpers
      getPrimaryColor: () => theme.colors.primary[500],
      getAccentColor: () => theme.colors.accent[500],
      getSecondaryColor: () => theme.colors.secondary[500],
    };
  }, [user?.gender, theme]);
  
  return genderInfo;
}

/**
 * Hook for responsive design based on screen size
 */
export function useResponsiveTheme() {
  const theme = useTheme();
  
  // For now, we'll return the base theme
  // In the future, this could include screen size detection
  // and responsive spacing/typography adjustments
  
  return {
    theme,
    
    // Responsive helpers
    getResponsiveSpacing: (base: keyof typeof theme.spacing, scale: number = 1) => {
      return theme.spacing[base] * scale;
    },
    
    getResponsiveFontSize: (base: keyof typeof theme.typography.fontSize, scale: number = 1) => {
      return theme.typography.fontSize[base] * scale;
    },
    
    // Breakpoint helpers (for future use)
    isSmallScreen: false, // TODO: Implement screen size detection
    isMediumScreen: true,
    isLargeScreen: false,
  };
}

/**
 * Hook for animation and transition values based on theme
 */
export function useThemeAnimations() {
  const theme = useTheme();
  
  return useMemo(() => ({
    // Standard durations
    duration: {
      fast: 150,
      normal: 250,
      slow: 350,
    },
    
    // Easing curves
    easing: {
      easeInOut: 'ease-in-out',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      linear: 'linear',
    },
    
    // Common animation configs for React Native Animated
    fadeIn: {
      duration: 250,
      useNativeDriver: true,
    },
    
    slideIn: {
      duration: 300,
      useNativeDriver: true,
    },
    
    spring: {
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    },
    
    // Theme-specific animation values
    primaryColor: theme.colors.primary[500],
    accentColor: theme.colors.accent[500],
  }), [theme]);
}

/**
 * Utility function to create theme-aware component styles
 */
export function createThemedStyles<T extends Record<string, any>>(
  styleFactory: (theme: Theme) => T
) {
  return (theme: Theme) => styleFactory(theme);
}

/**
 * Utility function to get color with opacity
 */
export function getColorWithOpacity(color: string, opacity: number): string {
  // Simple implementation - in a real app, you might want to use a color library
  // This assumes hex colors
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0');
    return `#${hex}${alpha}`;
  }
  
  // For RGB colors, convert to RGBA
  if (color.startsWith('rgb(')) {
    return color.replace('rgb(', 'rgba(').replace(')', `, ${opacity})`);
  }
  
  return color;
}