import { useState, useEffect, useCallback } from 'react';
import { storageService } from '../services/storageService';
import { errorService } from '../services/errorService';

export interface UseLocalStorageOptions {
  defaultValue?: any;
  serialize?: (value: any) => string;
  deserialize?: (value: string) => any;
  onError?: (error: Error) => void;
}

export interface UseLocalStorageReturn<T> {
  value: T | null;
  loading: boolean;
  error: string | null;
  setValue: (value: T | ((prev: T | null) => T)) => Promise<void>;
  removeValue: () => Promise<void>;
  refresh: () => Promise<void>;
}

/**
 * Custom hook for managing local storage with React state synchronization.
 * Provides automatic loading, error handling, and state management.
 */
export function useLocalStorage<T = any>(
  key: string,
  options: UseLocalStorageOptions = {}
): UseLocalStorageReturn<T> {
  const {
    defaultValue = null,
    serialize = JSON.stringify,
    deserialize = JSON.parse,
    onError,
  } = options;

  const [value, setValue] = useState<T | null>(defaultValue);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load value from storage
  const loadValue = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const storedValue = await storageService.getItem<string>(key);
      
      if (storedValue !== null) {
        const parsedValue = deserialize(storedValue);
        setValue(parsedValue);
      } else {
        setValue(defaultValue);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load from storage';
      setError(errorMessage);
      setValue(defaultValue);
      
      // Log error
      errorService.logStorageError(
        `Failed to load value for key: ${key}`,
        { key, error: err },
        { action: 'useLocalStorage.loadValue' }
      );
      
      // Call custom error handler if provided
      if (onError && err instanceof Error) {
        onError(err);
      }
    } finally {
      setLoading(false);
    }
  }, [key, defaultValue, deserialize, onError]);

  // Save value to storage
  const saveValue = useCallback(async (newValue: T | ((prev: T | null) => T)) => {
    try {
      setError(null);
      
      const valueToSave = typeof newValue === 'function' 
        ? (newValue as (prev: T | null) => T)(value)
        : newValue;
      
      const serializedValue = serialize(valueToSave);
      await storageService.setItem(key, serializedValue);
      setValue(valueToSave);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save to storage';
      setError(errorMessage);
      
      // Log error
      errorService.logStorageError(
        `Failed to save value for key: ${key}`,
        { key, value: newValue, error: err },
        { action: 'useLocalStorage.saveValue' }
      );
      
      // Call custom error handler if provided
      if (onError && err instanceof Error) {
        onError(err);
      }
      
      throw err; // Re-throw so caller can handle
    }
  }, [key, value, serialize, onError]);

  // Remove value from storage
  const removeValue = useCallback(async () => {
    try {
      setError(null);
      await storageService.removeItem(key);
      setValue(defaultValue);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove from storage';
      setError(errorMessage);
      
      // Log error
      errorService.logStorageError(
        `Failed to remove value for key: ${key}`,
        { key, error: err },
        { action: 'useLocalStorage.removeValue' }
      );
      
      // Call custom error handler if provided
      if (onError && err instanceof Error) {
        onError(err);
      }
      
      throw err; // Re-throw so caller can handle
    }
  }, [key, defaultValue, onError]);

  // Refresh value from storage
  const refresh = useCallback(async () => {
    await loadValue();
  }, [loadValue]);

  // Load initial value
  useEffect(() => {
    loadValue();
  }, [loadValue]);

  return {
    value,
    loading,
    error,
    setValue: saveValue,
    removeValue,
    refresh,
  };
}

/**
 * Hook for storing and retrieving objects with automatic JSON serialization
 */
export function useLocalStorageObject<T extends Record<string, any>>(
  key: string,
  defaultValue: T | null = null
): UseLocalStorageReturn<T> {
  return useLocalStorage<T>(key, {
    defaultValue,
    serialize: JSON.stringify,
    deserialize: JSON.parse,
  });
}

/**
 * Hook for storing and retrieving arrays with automatic JSON serialization
 */
export function useLocalStorageArray<T>(
  key: string,
  defaultValue: T[] = []
): UseLocalStorageReturn<T[]> {
  return useLocalStorage<T[]>(key, {
    defaultValue,
    serialize: JSON.stringify,
    deserialize: JSON.parse,
  });
}

/**
 * Hook for storing and retrieving strings
 */
export function useLocalStorageString(
  key: string,
  defaultValue: string = ''
): UseLocalStorageReturn<string> {
  return useLocalStorage<string>(key, {
    defaultValue,
    serialize: (value: string) => value,
    deserialize: (value: string) => value,
  });
}

/**
 * Hook for storing and retrieving numbers
 */
export function useLocalStorageNumber(
  key: string,
  defaultValue: number = 0
): UseLocalStorageReturn<number> {
  return useLocalStorage<number>(key, {
    defaultValue,
    serialize: (value: number) => value.toString(),
    deserialize: (value: string) => parseFloat(value),
  });
}

/**
 * Hook for storing and retrieving booleans
 */
export function useLocalStorageBoolean(
  key: string,
  defaultValue: boolean = false
): UseLocalStorageReturn<boolean> {
  return useLocalStorage<boolean>(key, {
    defaultValue,
    serialize: (value: boolean) => value.toString(),
    deserialize: (value: string) => value === 'true',
  });
}

/**
 * Hook for managing a list of items with add/remove functionality
 */
export function useLocalStorageList<T>(
  key: string,
  defaultValue: T[] = []
) {
  const { value, loading, error, setValue, removeValue, refresh } = useLocalStorageArray<T>(key, defaultValue);

  const addItem = useCallback(async (item: T) => {
    await setValue(prev => [...(prev || []), item]);
  }, [setValue]);

  const removeItem = useCallback(async (index: number) => {
    await setValue(prev => {
      if (!prev) return [];
      return prev.filter((_, i) => i !== index);
    });
  }, [setValue]);

  const updateItem = useCallback(async (index: number, item: T) => {
    await setValue(prev => {
      if (!prev) return [item];
      const newList = [...prev];
      newList[index] = item;
      return newList;
    });
  }, [setValue]);

  const clearList = useCallback(async () => {
    await setValue([]);
  }, [setValue]);

  return {
    items: value || [],
    loading,
    error,
    addItem,
    removeItem,
    updateItem,
    clearList,
    removeValue,
    refresh,
  };
}

/**
 * Hook for managing user preferences with type safety
 */
export function useUserPreferences<T extends Record<string, any>>(
  defaultPreferences: T
) {
  const { value, loading, error, setValue, refresh } = useLocalStorageObject<T>(
    'user_preferences',
    defaultPreferences
  );

  const updatePreference = useCallback(async <K extends keyof T>(
    key: K,
    value: T[K]
  ) => {
    await setValue(prev => ({
      ...(prev || defaultPreferences),
      [key]: value,
    }));
  }, [setValue, defaultPreferences]);

  const resetPreferences = useCallback(async () => {
    await setValue(defaultPreferences);
  }, [setValue, defaultPreferences]);

  return {
    preferences: value || defaultPreferences,
    loading,
    error,
    updatePreference,
    resetPreferences,
    refresh,
  };
}