import { useState, useCallback, useMemo } from 'react';
import { useApi } from './useApi';
import { useLocalStorage } from './useLocalStorage';
import { errorService } from '../services/errorService';
import type {
  WorkoutPlan,
  WorkoutSession,
} from '../types/workout';

// Additional types for workout functionality
type WorkoutType = 'strength' | 'cardio' | 'flexibility' | 'balance';
type DifficultyLevel = 'beginner' | 'intermediate' | 'advanced';

interface UseWorkoutOptions {
  autoSave?: boolean;
  syncWithServer?: boolean;
}

interface WorkoutState {
  currentWorkout: WorkoutSession | null;
  isActive: boolean;
  startTime: Date | null;
  elapsedTime: number;
  currentExerciseIndex: number;
  currentSetIndex: number;
  restTimer: number;
  isResting: boolean;
}

interface WorkoutFilters {
  type?: WorkoutType;
  difficulty?: DifficultyLevel;
  duration?: { min: number; max: number };
  searchQuery?: string;
}

interface WorkoutStats {
  totalWorkouts: number;
  totalDuration: number;
  averageDuration: number;
  favoriteWorkoutType: WorkoutType | null;
  streakDays: number;
  lastWorkoutDate: Date | null;
  weeklyGoalProgress: number;
}

/**
 * Custom hook for workout management and tracking
 */
export const useWorkout = (options: UseWorkoutOptions = {}) => {
  const { autoSave = true, syncWithServer = true } = options;
  const api = useApi();
  const localStorage = useLocalStorage('workoutData');

  // Workout state
  const [workoutState, setWorkoutState] = useState<WorkoutState>({
    currentWorkout: null,
    isActive: false,
    startTime: null,
    elapsedTime: 0,
    currentExerciseIndex: 0,
    currentSetIndex: 0,
    restTimer: 0,
    isResting: false,
  });

  const [workouts, setWorkouts] = useState<WorkoutPlan[]>([]);
  const [workoutHistory, setWorkoutHistory] = useState<WorkoutSession[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Start a new workout session
  const startWorkout = useCallback(async (workout: WorkoutPlan) => {
    try {
      setIsLoading(true);
      setError(null);

      const session: WorkoutSession = {
        id: `session_${Date.now()}`,
        workoutPlanId: workout.id,
        userId: 'current_user', // TODO: Get from user store
        startTime: new Date(),
        endTime: undefined,
        isActive: true,
        currentExerciseIndex: 0,
        currentSet: 0,
        exerciseResults: [],
        totalCaloriesBurned: 0,
      };

      setWorkoutState({
        currentWorkout: session,
        isActive: true,
        startTime: new Date(),
        elapsedTime: 0,
        currentExerciseIndex: 0,
        currentSetIndex: 0,
        restTimer: 0,
        isResting: false,
      });

      if (autoSave) {
        await localStorage.setValue(session);
      }

      if (syncWithServer) {
        await api.execute('/api/workout-sessions', { method: 'POST', body: session });
      }

      return session;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start workout';
      setError(errorMessage);
      errorService.logCriticalError('Failed to start workout', err as Error, { action: 'useWorkout.startWorkout' });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [autoSave, syncWithServer, api, localStorage]);

  // Complete current workout
  const completeWorkout = useCallback(async (_notes?: string) => {
    try {
      if (!workoutState.currentWorkout || !workoutState.isActive) {
        throw new Error('No active workout to complete');
      }

      setIsLoading(true);
      const endTime = new Date();

      const completedSession: WorkoutSession = {
        ...workoutState.currentWorkout,
        endTime,
        isActive: false,
      };

      setWorkoutHistory(prev => [completedSession, ...prev]);
      setWorkoutState({
        currentWorkout: null,
        isActive: false,
        startTime: null,
        elapsedTime: 0,
        currentExerciseIndex: 0,
        currentSetIndex: 0,
        restTimer: 0,
        isResting: false,
      });

      if (autoSave) {
        await localStorage.setValue(null);
      }

      if (syncWithServer) {
        await api.execute(`/api/workout-sessions/${completedSession.id}`, { 
          method: 'PUT', 
          body: completedSession 
        });
      }

      return completedSession;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to complete workout';
      setError(errorMessage);
      errorService.logCriticalError('Failed to complete workout', err as Error, { action: 'useWorkout.completeWorkout' });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [workoutState, autoSave, syncWithServer, api, localStorage]);

  // Pause/resume workout
  const pauseWorkout = useCallback(() => {
    if (workoutState.isActive) {
      setWorkoutState(prev => ({ ...prev, isActive: false }));
    }
  }, [workoutState.isActive]);

  const resumeWorkout = useCallback(() => {
    if (!workoutState.isActive && workoutState.currentWorkout) {
      setWorkoutState(prev => ({ ...prev, isActive: true }));
    }
  }, [workoutState.isActive, workoutState.currentWorkout]);

  // Move to next exercise
  const nextExercise = useCallback(() => {
    setWorkoutState(prev => ({
      ...prev,
      currentExerciseIndex: prev.currentExerciseIndex + 1,
      currentSetIndex: 0,
    }));
  }, []);

  // Start rest timer
  const startRest = useCallback((duration: number) => {
    setWorkoutState(prev => ({
      ...prev,
      isResting: true,
      restTimer: duration,
    }));
  }, []);

  // End rest timer
  const endRest = useCallback(() => {
    setWorkoutState(prev => ({
      ...prev,
      isResting: false,
      restTimer: 0,
    }));
  }, []);

  // Fetch workouts with filters
  const fetchWorkouts = useCallback(async (filters?: WorkoutFilters) => {
    try {
      setIsLoading(true);
      setError(null);

      const queryParams = new URLSearchParams();
      if (filters?.type) queryParams.append('type', filters.type);
      if (filters?.difficulty) queryParams.append('difficulty', filters.difficulty);
      if (filters?.searchQuery) queryParams.append('search', filters.searchQuery);

      const response = await api.execute(`/api/workouts?${queryParams.toString()}`);
      setWorkouts(response.data || []);
      return response.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch workouts';
      setError(errorMessage);
      errorService.logCriticalError('Failed to fetch workouts', err as Error, { action: 'useWorkout.fetchWorkouts' });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api]);

  // Fetch workout history
  const fetchWorkoutHistory = useCallback(async (limit?: number) => {
    try {
      setIsLoading(true);
      const queryParams = limit ? `?limit=${limit}` : '';
      const response = await api.execute(`/api/workout-sessions${queryParams}`);
      setWorkoutHistory(response.data || []);
      return response.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch workout history';
      setError(errorMessage);
      errorService.logCriticalError('Failed to fetch workout history', err as Error, { action: 'useWorkout.fetchWorkoutHistory' });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api]);

  // Calculate workout statistics
  const workoutStats = useMemo((): WorkoutStats => {
    const completedWorkouts = workoutHistory.filter(session => !session.isActive);
    const totalWorkouts = completedWorkouts.length;
    
    // Calculate total duration
    const totalDuration = completedWorkouts.reduce((sum, session) => {
      if (session.endTime) {
        return sum + (new Date(session.endTime).getTime() - new Date(session.startTime).getTime());
      }
      return sum;
    }, 0);
    
    const averageDuration = totalWorkouts > 0 ? totalDuration / totalWorkouts : 0;

    // For now, return basic stats
    const lastWorkoutDate = completedWorkouts.length > 0 
      ? new Date(completedWorkouts[0].startTime) 
      : null;

    return {
      totalWorkouts,
      totalDuration,
      averageDuration,
      favoriteWorkoutType: null, // TODO: Implement based on workout plans
      streakDays: 0, // TODO: Calculate streak
      lastWorkoutDate,
      weeklyGoalProgress: 0, // TODO: Calculate weekly progress
    };
  }, [workoutHistory]);

  // Get current exercise
  const currentExercise = useMemo(() => {
    if (!workoutState.currentWorkout) {
      return null;
    }
    // TODO: Get exercise from workout plan based on currentExerciseIndex
    return null;
  }, [workoutState.currentWorkout]);

  // Check if workout is completed
  const isWorkoutCompleted = useMemo(() => {
    if (!workoutState.currentWorkout) return false;
    // TODO: Implement based on workout plan exercises
    return false;
  }, [workoutState.currentWorkout]);

  return {
    // State
    workoutState,
    workouts,
    workoutHistory,
    isLoading,
    error,
    workoutStats,
    currentExercise,
    isWorkoutCompleted,

    // Actions
    startWorkout,
    completeWorkout,
    pauseWorkout,
    resumeWorkout,
    nextExercise,
    startRest,
    endRest,
    fetchWorkouts,
    fetchWorkoutHistory,

    // Utilities
    clearError: () => setError(null),
  };
};

/**
 * Hook for workout timer functionality
 */
export const useWorkoutTimer = () => {
  const [time, setTime] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [intervalId, setIntervalId] = useState<number | null>(null);

  const start = useCallback(() => {
    if (!isRunning) {
      setIsRunning(true);
      const id = setInterval(() => {
        setTime(prev => prev + 1);
      }, 1000);
      setIntervalId(id);
    }
  }, [isRunning]);

  const pause = useCallback(() => {
    if (isRunning && intervalId) {
      setIsRunning(false);
      clearInterval(intervalId);
      setIntervalId(null);
    }
  }, [isRunning, intervalId]);

  const reset = useCallback(() => {
    setTime(0);
    setIsRunning(false);
    if (intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
    }
  }, [intervalId]);

  const formatTime = useCallback((seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  return {
    time,
    isRunning,
    start,
    pause,
    reset,
    formatTime,
    formattedTime: formatTime(time),
  };
};

/**
 * Hook for rest timer functionality
 */
export const useRestTimer = (initialDuration: number = 60) => {
  const [duration, setDuration] = useState(initialDuration);
  const [timeLeft, setTimeLeft] = useState(initialDuration);
  const [isActive, setIsActive] = useState(false);
  const [intervalId, setIntervalId] = useState<number | null>(null);

  const start = useCallback((customDuration?: number) => {
    const restDuration = customDuration || duration;
    setTimeLeft(restDuration);
    setIsActive(true);
    
    const id = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          setIsActive(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    setIntervalId(id);
  }, [duration]);

  const pause = useCallback(() => {
    setIsActive(false);
    if (intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
    }
  }, [intervalId]);

  const reset = useCallback(() => {
    setTimeLeft(duration);
    setIsActive(false);
    if (intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
    }
  }, [duration, intervalId]);

  const skip = useCallback(() => {
    setTimeLeft(0);
    setIsActive(false);
    if (intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
    }
  }, [intervalId]);

  const addTime = useCallback((seconds: number) => {
    setTimeLeft(prev => prev + seconds);
  }, []);

  const formatTime = useCallback((seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  return {
    duration,
    timeLeft,
    isActive,
    isCompleted: timeLeft === 0 && !isActive,
    start,
    pause,
    reset,
    skip,
    addTime,
    setDuration,
    formatTime,
    formattedTime: formatTime(timeLeft),
    progress: duration > 0 ? ((duration - timeLeft) / duration) * 100 : 0,
  };
};