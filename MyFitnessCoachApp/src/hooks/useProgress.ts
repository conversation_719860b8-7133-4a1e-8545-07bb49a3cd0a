import { useState, useCallback, useMemo } from 'react';
import { useApi } from './useApi';
import { useLocalStorage } from './useLocalStorage';
import { errorService } from '../services/errorService';

// Progress types
interface BodyMeasurement {
  id: string;
  date: string; // YYYY-MM-DD format
  weight?: number; // kg
  bodyFat?: number; // percentage
  muscleMass?: number; // kg
  height?: number; // cm
  chest?: number; // cm
  waist?: number; // cm
  hips?: number; // cm
  biceps?: number; // cm
  thighs?: number; // cm
  notes?: string;
}

interface WorkoutProgress {
  exerciseId: string;
  exerciseName: string;
  date: string;
  sets: {
    reps: number;
    weight: number;
    restTime?: number;
  }[];
  totalVolume: number; // weight * reps * sets
  personalRecord?: boolean;
}

interface ProgressGoal {
  id: string;
  type: 'weight_loss' | 'weight_gain' | 'muscle_gain' | 'strength' | 'endurance' | 'custom';
  title: string;
  description: string;
  targetValue: number;
  currentValue: number;
  unit: string;
  targetDate: string;
  isCompleted: boolean;
  createdAt: string;
}

interface ProgressStats {
  totalWorkouts: number;
  totalWorkoutTime: number; // minutes
  averageWorkoutTime: number; // minutes
  currentStreak: number; // days
  longestStreak: number; // days
  totalCaloriesBurned: number;
  averageCaloriesPerWorkout: number;
  strengthProgress: {
    exerciseId: string;
    exerciseName: string;
    improvement: number; // percentage
    timeframe: string;
  }[];
  bodyComposition: {
    weightChange: number;
    bodyFatChange: number;
    muscleMassChange: number;
    timeframe: string;
  };
}

interface ProgressChart {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    color: string;
  }[];
}

interface UseProgressOptions {
  autoSync?: boolean;
  cacheResults?: boolean;
}

/**
 * Custom hook for tracking fitness progress and analytics
 */
export const useProgress = (options: UseProgressOptions = {}) => {
  const { autoSync = true, cacheResults = true } = options;
  const api = useApi();
  const localStorage = useLocalStorage('progressData');

  // State
  const [bodyMeasurements, setBodyMeasurements] = useState<BodyMeasurement[]>([]);
  const [workoutProgress, setWorkoutProgress] = useState<WorkoutProgress[]>([]);
  const [goals, setGoals] = useState<ProgressGoal[]>([]);
  const [progressStats, setProgressStats] = useState<ProgressStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Add body measurement
  const addBodyMeasurement = useCallback(async (measurement: Omit<BodyMeasurement, 'id'>) => {
    try {
      setIsLoading(true);
      setError(null);

      const newMeasurement: BodyMeasurement = {
        ...measurement,
        id: `measurement_${Date.now()}`,
      };

      setBodyMeasurements(prev => {
        const updated = [...prev, newMeasurement].sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        
        if (cacheResults) {
          localStorage.setValue(updated);
        }
        
        return updated;
      });

      if (autoSync) {
        await api.execute('/api/progress/measurements', {
          method: 'POST',
          body: newMeasurement,
        });
      }

      return newMeasurement;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add body measurement';
      setError(errorMessage);
      errorService.logCriticalError('Failed to add body measurement', err as Error, {
        action: 'useProgress.addBodyMeasurement',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api, autoSync, cacheResults, localStorage]);

  // Update body measurement
  const updateBodyMeasurement = useCallback(async (id: string, updates: Partial<BodyMeasurement>) => {
    try {
      setIsLoading(true);
      setError(null);

      setBodyMeasurements(prev => {
        const updated = prev.map(measurement => 
          measurement.id === id ? { ...measurement, ...updates } : measurement
        );
        
        if (cacheResults) {
          localStorage.setValue(updated);
        }
        
        return updated;
      });

      if (autoSync) {
        await api.execute(`/api/progress/measurements/${id}`, {
          method: 'PUT',
          body: updates,
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update body measurement';
      setError(errorMessage);
      errorService.logCriticalError('Failed to update body measurement', err as Error, {
        action: 'useProgress.updateBodyMeasurement',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api, autoSync, cacheResults, localStorage]);

  // Delete body measurement
  const deleteBodyMeasurement = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      setError(null);

      setBodyMeasurements(prev => {
        const updated = prev.filter(measurement => measurement.id !== id);
        
        if (cacheResults) {
          localStorage.setValue(updated);
        }
        
        return updated;
      });

      if (autoSync) {
        await api.execute(`/api/progress/measurements/${id}`, {
          method: 'DELETE',
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete body measurement';
      setError(errorMessage);
      errorService.logCriticalError('Failed to delete body measurement', err as Error, {
        action: 'useProgress.deleteBodyMeasurement',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api, autoSync, cacheResults, localStorage]);

  // Add workout progress
  const addWorkoutProgress = useCallback(async (progress: Omit<WorkoutProgress, 'totalVolume' | 'personalRecord'>) => {
    try {
      setIsLoading(true);
      setError(null);

      // Calculate total volume
      const totalVolume = progress.sets.reduce((total, set) => total + (set.weight * set.reps), 0);
      
      // Check if it's a personal record
      const existingProgress = workoutProgress.filter(p => p.exerciseId === progress.exerciseId);
      const maxVolume = Math.max(...existingProgress.map(p => p.totalVolume), 0);
      const personalRecord = totalVolume > maxVolume;

      const newProgress: WorkoutProgress = {
        ...progress,
        totalVolume,
        personalRecord,
      };

      setWorkoutProgress(prev => {
        const updated = [...prev, newProgress].sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        
        if (cacheResults) {
          localStorage.setValue(updated);
        }
        
        return updated;
      });

      if (autoSync) {
        await api.execute('/api/progress/workouts', {
          method: 'POST',
          body: newProgress,
        });
      }

      return newProgress;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add workout progress';
      setError(errorMessage);
      errorService.logCriticalError('Failed to add workout progress', err as Error, {
        action: 'useProgress.addWorkoutProgress',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api, autoSync, cacheResults, localStorage, workoutProgress]);

  // Create progress goal
  const createGoal = useCallback(async (goal: Omit<ProgressGoal, 'id' | 'isCompleted' | 'createdAt'>) => {
    try {
      setIsLoading(true);
      setError(null);

      const newGoal: ProgressGoal = {
        ...goal,
        id: `goal_${Date.now()}`,
        isCompleted: false,
        createdAt: new Date().toISOString(),
      };

      setGoals(prev => {
        const updated = [...prev, newGoal];
        
        if (cacheResults) {
          localStorage.setValue(updated);
        }
        
        return updated;
      });

      if (autoSync) {
        await api.execute('/api/progress/goals', {
          method: 'POST',
          body: newGoal,
        });
      }

      return newGoal;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create goal';
      setError(errorMessage);
      errorService.logCriticalError('Failed to create goal', err as Error, {
        action: 'useProgress.createGoal',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api, autoSync, cacheResults, localStorage]);

  // Update goal progress
  const updateGoalProgress = useCallback(async (goalId: string, currentValue: number) => {
    try {
      setIsLoading(true);
      setError(null);

      setGoals(prev => {
        const updated = prev.map(goal => {
          if (goal.id === goalId) {
            const isCompleted = currentValue >= goal.targetValue;
            return { ...goal, currentValue, isCompleted };
          }
          return goal;
        });
        
        if (cacheResults) {
          localStorage.setValue(updated);
        }
        
        return updated;
      });

      if (autoSync) {
        await api.execute(`/api/progress/goals/${goalId}`, {
          method: 'PUT',
          body: { currentValue },
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update goal progress';
      setError(errorMessage);
      errorService.logCriticalError('Failed to update goal progress', err as Error, {
        action: 'useProgress.updateGoalProgress',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api, autoSync, cacheResults, localStorage]);

  // Get progress statistics
  const getProgressStats = useCallback(async (timeframe: 'week' | 'month' | 'quarter' | 'year' = 'month') => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await api.execute(`/api/progress/stats?timeframe=${timeframe}`);
      const stats = response.data;
      
      setProgressStats(stats);
      return stats;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get progress statistics';
      setError(errorMessage);
      errorService.logCriticalError('Failed to get progress statistics', err as Error, {
        action: 'useProgress.getProgressStats',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api]);

  // Generate progress charts
  const generateProgressChart = useCallback((
    type: 'weight' | 'bodyFat' | 'strength' | 'volume',
    exerciseId?: string,
    timeframe: 'week' | 'month' | 'quarter' | 'year' = 'month'
  ): ProgressChart => {
    const now = new Date();
    const startDate = new Date();
    
    switch (timeframe) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
    }

    if (type === 'weight' || type === 'bodyFat') {
      const filteredMeasurements = bodyMeasurements.filter(
        m => new Date(m.date) >= startDate
      ).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      return {
        labels: filteredMeasurements.map(m => m.date),
        datasets: [{
          label: type === 'weight' ? 'Weight (kg)' : 'Body Fat (%)',
          data: filteredMeasurements.map(m => type === 'weight' ? m.weight || 0 : m.bodyFat || 0),
          color: type === 'weight' ? '#3B82F6' : '#EF4444',
        }],
      };
    }

    if (type === 'strength' || type === 'volume') {
      const filteredProgress = workoutProgress.filter(
        p => new Date(p.date) >= startDate && (!exerciseId || p.exerciseId === exerciseId)
      ).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      return {
        labels: filteredProgress.map(p => p.date),
        datasets: [{
          label: type === 'strength' ? 'Max Weight (kg)' : 'Total Volume (kg)',
          data: filteredProgress.map(p => 
            type === 'strength' 
              ? Math.max(...p.sets.map(s => s.weight))
              : p.totalVolume
          ),
          color: '#10B981',
        }],
      };
    }

    return { labels: [], datasets: [] };
  }, [bodyMeasurements, workoutProgress]);

  // Get latest measurements
  const latestMeasurements = useMemo(() => {
    return bodyMeasurements.length > 0 ? bodyMeasurements[0] : null;
  }, [bodyMeasurements]);

  // Get active goals
  const activeGoals = useMemo(() => {
    return goals.filter(goal => !goal.isCompleted);
  }, [goals]);

  // Get completed goals
  const completedGoals = useMemo(() => {
    return goals.filter(goal => goal.isCompleted);
  }, [goals]);

  // Get personal records
  const personalRecords = useMemo(() => {
    return workoutProgress.filter(progress => progress.personalRecord);
  }, [workoutProgress]);

  return {
    // State
    bodyMeasurements,
    workoutProgress,
    goals,
    progressStats,
    latestMeasurements,
    activeGoals,
    completedGoals,
    personalRecords,
    isLoading,
    error,

    // Actions
    addBodyMeasurement,
    updateBodyMeasurement,
    deleteBodyMeasurement,
    addWorkoutProgress,
    createGoal,
    updateGoalProgress,
    getProgressStats,
    generateProgressChart,

    // Utilities
    clearError: () => setError(null),
  };
};

/**
 * Hook for progress analytics and insights
 */
export const useProgressAnalytics = () => {
  const [insights, setInsights] = useState<string[]>([]);
  const [trends, setTrends] = useState<{
    type: string;
    direction: 'up' | 'down' | 'stable';
    percentage: number;
    description: string;
  }[]>([]);

  const generateInsights = useCallback((progressData: {
    bodyMeasurements: BodyMeasurement[];
    workoutProgress: WorkoutProgress[];
    goals: ProgressGoal[];
  }) => {
    const newInsights: string[] = [];
    const newTrends: typeof trends = [];

    // Weight trend analysis
    if (progressData.bodyMeasurements.length >= 2) {
      const recent = progressData.bodyMeasurements.slice(0, 2);
      const weightChange = (recent[0].weight || 0) - (recent[1].weight || 0);
      
      if (Math.abs(weightChange) > 0.5) {
        const direction = weightChange > 0 ? 'up' : 'down';
        const percentage = Math.abs((weightChange / (recent[1].weight || 1)) * 100);
        
        newTrends.push({
          type: 'weight',
          direction,
          percentage,
          description: `Weight ${direction === 'up' ? 'increased' : 'decreased'} by ${Math.abs(weightChange).toFixed(1)}kg`,
        });
      }
    }

    // Workout consistency analysis
    const recentWorkouts = progressData.workoutProgress.filter(
      p => new Date(p.date) >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    );
    
    if (recentWorkouts.length >= 8) {
      newInsights.push('Great consistency! You\'ve been working out regularly this month.');
    } else if (recentWorkouts.length >= 4) {
      newInsights.push('Good progress! Try to maintain consistency for better results.');
    } else {
      newInsights.push('Consider increasing workout frequency for better progress.');
    }

    // Goal progress analysis
    const nearCompletionGoals = progressData.goals.filter(
      goal => !goal.isCompleted && (goal.currentValue / goal.targetValue) >= 0.8
    );
    
    if (nearCompletionGoals.length > 0) {
      newInsights.push(`You're close to achieving ${nearCompletionGoals.length} goal(s)! Keep pushing!`);
    }

    setInsights(newInsights);
    setTrends(newTrends);
  }, []);

  return {
    insights,
    trends,
    generateInsights,
  };
};