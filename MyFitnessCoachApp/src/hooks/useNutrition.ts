import { useState, useCallback, useMemo } from 'react';
import { useApi } from './useApi';
import { useLocalStorage } from './useLocalStorage';
import { errorService } from '../services/errorService';

// Nutrition types
interface NutritionInfo {
  calories: number;
  protein: number; // grams
  carbs: number; // grams
  fat: number; // grams
  fiber?: number; // grams
  sugar?: number; // grams
  sodium?: number; // mg
}

interface Food {
  id: string;
  name: string;
  brand?: string;
  category: string;
  nutritionPer100g: NutritionInfo;
  servingSizes: {
    name: string;
    grams: number;
  }[];
  barcode?: string;
}

interface FoodEntry {
  id: string;
  foodId: string;
  food: Food;
  quantity: number; // in grams
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  timestamp: Date;
  notes?: string;
}

interface DailyNutrition {
  date: string; // YYYY-MM-DD format
  entries: FoodEntry[];
  totalNutrition: NutritionInfo;
  waterIntake: number; // in ml
  goals: NutritionGoals;
}

interface NutritionGoals {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  water: number; // in ml
}

interface MealPlan {
  id: string;
  name: string;
  description: string;
  duration: number; // days
  targetGoal: 'weight_loss' | 'muscle_gain' | 'maintenance';
  dailyMeals: {
    day: number;
    meals: {
      type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
      name: string;
      foods: {
        foodId: string;
        quantity: number;
      }[];
    }[];
  }[];
}

interface UseNutritionOptions {
  autoSave?: boolean;
  syncWithServer?: boolean;
}

/**
 * Custom hook for nutrition tracking and meal planning
 */
export const useNutrition = (options: UseNutritionOptions = {}) => {
  const { autoSave = true, syncWithServer = true } = options;
  const api = useApi();
  const localStorage = useLocalStorage('nutritionData');

  // State
  const [foods, setFoods] = useState<Food[]>([]);
  const [dailyNutrition, setDailyNutrition] = useState<DailyNutrition | null>(null);
  const [nutritionHistory, _setNutritionHistory] = useState<DailyNutrition[]>([]);
  const [mealPlans, _setMealPlans] = useState<MealPlan[]>([]);
  const [nutritionGoals, setNutritionGoals] = useState<NutritionGoals>({
    calories: 2000,
    protein: 150,
    carbs: 250,
    fat: 65,
    water: 2000,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Calculate nutrition for a given quantity
  const calculateNutrition = useCallback((food: Food, quantity: number): NutritionInfo => {
    const multiplier = quantity / 100; // nutrition is per 100g
    return {
      calories: Math.round(food.nutritionPer100g.calories * multiplier),
      protein: Math.round(food.nutritionPer100g.protein * multiplier * 10) / 10,
      carbs: Math.round(food.nutritionPer100g.carbs * multiplier * 10) / 10,
      fat: Math.round(food.nutritionPer100g.fat * multiplier * 10) / 10,
      fiber: food.nutritionPer100g.fiber ? Math.round(food.nutritionPer100g.fiber * multiplier * 10) / 10 : undefined,
      sugar: food.nutritionPer100g.sugar ? Math.round(food.nutritionPer100g.sugar * multiplier * 10) / 10 : undefined,
      sodium: food.nutritionPer100g.sodium ? Math.round(food.nutritionPer100g.sodium * multiplier) : undefined,
    };
  }, []);

  // Calculate total nutrition from entries
  const calculateTotalNutrition = useCallback((entries: FoodEntry[]): NutritionInfo => {
    return entries.reduce<NutritionInfo>(
      (total, entry) => {
        const entryNutrition = calculateNutrition(entry.food, entry.quantity);
        return {
          calories: total.calories + entryNutrition.calories,
          protein: Math.round((total.protein + entryNutrition.protein) * 10) / 10,
          carbs: Math.round((total.carbs + entryNutrition.carbs) * 10) / 10,
          fat: Math.round((total.fat + entryNutrition.fat) * 10) / 10,
          fiber: total.fiber && entryNutrition.fiber 
            ? Math.round((total.fiber + entryNutrition.fiber) * 10) / 10 
            : total.fiber || entryNutrition.fiber,
          sugar: total.sugar && entryNutrition.sugar 
            ? Math.round((total.sugar + entryNutrition.sugar) * 10) / 10 
            : total.sugar || entryNutrition.sugar,
          sodium: total.sodium && entryNutrition.sodium 
            ? total.sodium + entryNutrition.sodium 
            : total.sodium || entryNutrition.sodium,
        };
      },
      { 
        calories: 0, 
        protein: 0, 
        carbs: 0, 
        fat: 0,
        fiber: undefined,
        sugar: undefined,
        sodium: undefined
      }
    );
  }, [calculateNutrition]);

  // Add food entry
  const addFoodEntry = useCallback(async (
    foodId: string,
    quantity: number,
    mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack',
    notes?: string
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      // Find the food
      const food = foods.find(f => f.id === foodId);
      if (!food) {
        throw new Error('Food not found');
      }

      const entry: FoodEntry = {
        id: `entry_${Date.now()}`,
        foodId,
        food,
        quantity,
        mealType,
        timestamp: new Date(),
        notes,
      };

      // Get today's date
      const today = new Date().toISOString().split('T')[0];
      
      // Update daily nutrition
      setDailyNutrition(prev => {
        if (!prev || prev.date !== today) {
          // Create new daily nutrition for today
          const newDaily: DailyNutrition = {
            date: today,
            entries: [entry],
            totalNutrition: calculateNutrition(food, quantity),
            waterIntake: 0,
            goals: nutritionGoals,
          };
          return newDaily;
        } else {
          // Add to existing daily nutrition
          const updatedEntries = [...prev.entries, entry];
          const totalNutrition = calculateTotalNutrition(updatedEntries);
          return {
            ...prev,
            entries: updatedEntries,
            totalNutrition,
          };
        }
      });

      if (autoSave) {
        await localStorage.setValue(dailyNutrition);
      }

      if (syncWithServer) {
        await api.execute('/api/nutrition/entries', { method: 'POST', body: entry });
      }

      return entry;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add food entry';
      setError(errorMessage);
      errorService.logCriticalError('Failed to add food entry', err as Error, { action: 'useNutrition.addFoodEntry' });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [foods, calculateNutrition, nutritionGoals, dailyNutrition, autoSave, syncWithServer, api, localStorage, calculateTotalNutrition]);

  // Remove food entry
  const removeFoodEntry = useCallback(async (entryId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      setDailyNutrition(prev => {
        if (!prev) return null;
        
        const updatedEntries = prev.entries.filter(entry => entry.id !== entryId);
        const totalNutrition = calculateTotalNutrition(updatedEntries);
        
        return {
          ...prev,
          entries: updatedEntries,
          totalNutrition,
        };
      });

      if (syncWithServer) {
        await api.execute(`/api/nutrition/entries/${entryId}`, { method: 'DELETE' });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove food entry';
      setError(errorMessage);
      errorService.logCriticalError('Failed to remove food entry', err as Error, { action: 'useNutrition.removeFoodEntry' });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [calculateTotalNutrition, syncWithServer, api]);

  // Update water intake
  const updateWaterIntake = useCallback(async (amount: number) => {
    try {
      setDailyNutrition(prev => {
        if (!prev) return null;
        return {
          ...prev,
          waterIntake: prev.waterIntake + amount,
        };
      });

      if (syncWithServer) {
        await api.execute('/api/nutrition/water', { 
          method: 'POST', 
          body: { amount, date: new Date().toISOString().split('T')[0] } 
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update water intake';
      setError(errorMessage);
      errorService.logCriticalError('Failed to update water intake', err as Error, { action: 'useNutrition.updateWaterIntake' });
      throw err;
    }
  }, [syncWithServer, api]);

  // Search foods
  const searchFoods = useCallback(async (query: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await api.execute(`/api/foods/search?q=${encodeURIComponent(query)}`);
      const searchResults = response.data || [];
      
      // Update foods list with search results
      setFoods(prev => {
        const existingIds = new Set(prev.map(f => f.id));
        const newFoods = searchResults.filter((food: Food) => !existingIds.has(food.id));
        return [...prev, ...newFoods];
      });

      return searchResults;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to search foods';
      setError(errorMessage);
      errorService.logCriticalError('Failed to search foods', err as Error, { action: 'useNutrition.searchFoods' });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api]);

  // Get nutrition for date
  const getNutritionForDate = useCallback(async (date: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await api.execute(`/api/nutrition/daily/${date}`);
      const dailyData = response.data;
      
      if (dailyData) {
        setDailyNutrition(dailyData);
      }
      
      return dailyData;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get nutrition data';
      setError(errorMessage);
      errorService.logCriticalError('Failed to get nutrition data', err as Error, { action: 'useNutrition.getNutritionForDate' });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api]);

  // Update nutrition goals
  const updateNutritionGoals = useCallback(async (goals: Partial<NutritionGoals>) => {
    try {
      const updatedGoals = { ...nutritionGoals, ...goals };
      setNutritionGoals(updatedGoals);

      if (syncWithServer) {
        await api.execute('/api/nutrition/goals', { method: 'PUT', body: updatedGoals });
      }

      return updatedGoals;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update nutrition goals';
      setError(errorMessage);
      errorService.logCriticalError('Failed to update nutrition goals', err as Error, { action: 'useNutrition.updateNutritionGoals' });
      throw err;
    }
  }, [nutritionGoals, syncWithServer, api]);

  // Calculate progress towards goals
  const nutritionProgress = useMemo(() => {
    if (!dailyNutrition) {
      return {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        water: 0,
      };
    }

    return {
      calories: Math.round((dailyNutrition.totalNutrition.calories / nutritionGoals.calories) * 100),
      protein: Math.round((dailyNutrition.totalNutrition.protein / nutritionGoals.protein) * 100),
      carbs: Math.round((dailyNutrition.totalNutrition.carbs / nutritionGoals.carbs) * 100),
      fat: Math.round((dailyNutrition.totalNutrition.fat / nutritionGoals.fat) * 100),
      water: Math.round((dailyNutrition.waterIntake / nutritionGoals.water) * 100),
    };
  }, [dailyNutrition, nutritionGoals]);

  // Get meals by type for today
  const getMealsByType = useCallback((mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack') => {
    if (!dailyNutrition) return [];
    return dailyNutrition.entries.filter(entry => entry.mealType === mealType);
  }, [dailyNutrition]);

  return {
    // State
    foods,
    dailyNutrition,
    nutritionHistory,
    mealPlans,
    nutritionGoals,
    nutritionProgress,
    isLoading,
    error,

    // Actions
    addFoodEntry,
    removeFoodEntry,
    updateWaterIntake,
    searchFoods,
    getNutritionForDate,
    updateNutritionGoals,
    calculateNutrition,
    getMealsByType,

    // Utilities
    clearError: () => setError(null),
  };
};

/**
 * Hook for barcode scanning functionality
 */
export const useBarcodeScanner = () => {
  const [isScanning, setIsScanning] = useState(false);
  const [scannedCode, setScannedCode] = useState<string | null>(null);
  const api = useApi();

  const lookupByBarcode = useCallback(async (barcode: string) => {
    try {
      const response = await api.execute(`/api/foods/barcode/${barcode}`);
      return response.data;
    } catch (err) {
      errorService.logCriticalError('Failed to lookup food by barcode', err as Error, { action: 'useBarcodeScanner.lookupByBarcode' });
      throw err;
    }
  }, [api]);

  const startScanning = useCallback(() => {
    setIsScanning(true);
    setScannedCode(null);
  }, []);

  const stopScanning = useCallback(() => {
    setIsScanning(false);
  }, []);

  const handleScan = useCallback((code: string) => {
    setScannedCode(code);
    setIsScanning(false);
  }, []);

  return {
    isScanning,
    scannedCode,
    startScanning,
    stopScanning,
    handleScan,
    lookupByBarcode,
  };
};