// Custom hooks exports
export { useApi, useApiGet, useApiPost, useApiPut, useApiDelete } from './useApi';
export { useForm, useSimpleForm } from './useForm';
export { 
  useLocalStorage, 
  useLocalStorageObject, 
  useLocalStorageArray, 
  useLocalStorageString, 
  useLocalStorageNumber, 
  useLocalStorageBoolean, 
  useLocalStorageList, 
  useUserPreferences 
} from './useLocalStorage';
export { 
  useTheme, 
  useThemedStyles, 
  useGenderTheming, 
  useResponsiveTheme, 
  useThemeAnimations, 
  createThemedStyles, 
  getColorWithOpacity 
} from './useTheme';
export { useWorkout, useWorkoutTimer, useRestTimer } from './useWorkout';
export { useNutrition, useBarcodeScanner } from './useNutrition';
export { useProgress, useProgressAnalytics } from './useProgress';
export { 
  useNotifications, 
  useWorkoutReminders, 
  useMealReminders 
} from './useNotifications';

// Re-export types for convenience
export type { ApiResponse, ApiOptions, UseApiReturn } from './useApi';
export type { FormField, FormState, UseFormOptions, UseFormReturn } from './useForm';
export type { UseLocalStorageReturn } from './useLocalStorage';