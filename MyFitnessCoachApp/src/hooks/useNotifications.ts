import { useState, useCallback, useEffect } from 'react';
import { useApi } from './useApi';
import { useLocalStorage } from './useLocalStorage';
import { errorService } from '../services/errorService';

// Notification types
interface NotificationSettings {
  workoutReminders: boolean;
  mealReminders: boolean;
  progressUpdates: boolean;
  achievements: boolean;
  socialUpdates: boolean;
  systemUpdates: boolean;
  quietHours: {
    enabled: boolean;
    startTime: string; // HH:MM format
    endTime: string; // HH:MM format
  };
  frequency: {
    workouts: 'daily' | 'weekly' | 'custom';
    meals: 'every_meal' | 'daily' | 'custom';
    progress: 'weekly' | 'monthly' | 'custom';
  };
}

interface Notification {
  id: string;
  type: 'workout' | 'meal' | 'progress' | 'achievement' | 'social' | 'system';
  title: string;
  message: string;
  data?: Record<string, any>;
  isRead: boolean;
  isPush: boolean;
  scheduledFor?: string; // ISO string
  createdAt: string;
  expiresAt?: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  actionUrl?: string;
  imageUrl?: string;
}

interface ScheduledNotification {
  id: string;
  type: 'workout' | 'meal' | 'progress' | 'custom';
  title: string;
  message: string;
  scheduledFor: string; // ISO string
  repeatInterval?: 'daily' | 'weekly' | 'monthly';
  isActive: boolean;
  data?: Record<string, any>;
}

interface NotificationPermissions {
  granted: boolean;
  denied: boolean;
  provisional?: boolean;
  alert: boolean;
  badge: boolean;
  sound: boolean;
}

interface UseNotificationsOptions {
  autoRequestPermissions?: boolean;
  enableBadgeCount?: boolean;
  enableSound?: boolean;
}

/**
 * Custom hook for managing notifications and push notifications
 */
export const useNotifications = (options: UseNotificationsOptions = {}) => {
  const { autoRequestPermissions = true, enableBadgeCount = true, enableSound = true } = options;
  const api = useApi();
  const localStorage = useLocalStorage('notificationData');

  // State
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [scheduledNotifications, setScheduledNotifications] = useState<ScheduledNotification[]>([]);
  const [settings, setSettings] = useState<NotificationSettings>({
    workoutReminders: true,
    mealReminders: true,
    progressUpdates: true,
    achievements: true,
    socialUpdates: false,
    systemUpdates: true,
    quietHours: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00',
    },
    frequency: {
      workouts: 'daily',
      meals: 'every_meal',
      progress: 'weekly',
    },
  });
  const [permissions, setPermissions] = useState<NotificationPermissions>({
    granted: false,
    denied: false,
    alert: false,
    badge: false,
    sound: false,
  });
  const [pushToken, setPushToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Request notification permissions
  const requestPermissions = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // This would be platform-specific implementation
      // For React Native, you'd use @react-native-async-storage/async-storage
      // and react-native-push-notification or similar
      
      // Mock implementation for now
      const mockPermissions: NotificationPermissions = {
        granted: true,
        denied: false,
        alert: true,
        badge: enableBadgeCount,
        sound: enableSound,
      };

      setPermissions(mockPermissions);
      
      // Generate mock push token
      const mockToken = `push_token_${Date.now()}`;
      setPushToken(mockToken);

      // Register token with backend
      if (mockPermissions.granted) {
        await api.execute('/api/notifications/register', {
          method: 'POST',
          body: { token: mockToken, platform: 'ios' }, // or 'android'
        });
      }

      return mockPermissions;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to request permissions';
      setError(errorMessage);
      errorService.logCriticalError('Failed to request notification permissions', err as Error, {
        action: 'useNotifications.requestPermissions',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api, enableBadgeCount, enableSound]);

  // Update notification settings
  const updateSettings = useCallback(async (newSettings: Partial<NotificationSettings>) => {
    try {
      setIsLoading(true);
      setError(null);

      const updatedSettings = { ...settings, ...newSettings };
      setSettings(updatedSettings);

      // Save to local storage
      await localStorage.setValue(updatedSettings);

      // Sync with backend
      await api.execute('/api/notifications/settings', {
        method: 'PUT',
        body: updatedSettings,
      });

      return updatedSettings;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update settings';
      setError(errorMessage);
      errorService.logCriticalError('Failed to update notification settings', err as Error, {
        action: 'useNotifications.updateSettings',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [settings, localStorage, api]);

  // Schedule a notification
  const scheduleNotification = useCallback(async (
    notification: Omit<ScheduledNotification, 'id' | 'isActive'>
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const newNotification: ScheduledNotification = {
        ...notification,
        id: `scheduled_${Date.now()}`,
        isActive: true,
      };

      setScheduledNotifications(prev => [...prev, newNotification]);

      // Schedule with the system (platform-specific)
      // This would use react-native-push-notification or similar
      
      // Register with backend
      await api.execute('/api/notifications/schedule', {
        method: 'POST',
        body: newNotification,
      });

      return newNotification;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to schedule notification';
      setError(errorMessage);
      errorService.logCriticalError('Failed to schedule notification', err as Error, {
        action: 'useNotifications.scheduleNotification',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api]);

  // Cancel scheduled notification
  const cancelScheduledNotification = useCallback(async (notificationId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      setScheduledNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, isActive: false }
            : notification
        )
      );

      // Cancel with the system (platform-specific)
      
      // Update backend
      await api.execute(`/api/notifications/schedule/${notificationId}`, {
        method: 'DELETE',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to cancel notification';
      setError(errorMessage);
      errorService.logCriticalError('Failed to cancel scheduled notification', err as Error, {
        action: 'useNotifications.cancelScheduledNotification',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api]);

  // Send immediate notification
  const sendNotification = useCallback(async (
    notification: Omit<Notification, 'id' | 'isRead' | 'createdAt'>
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const newNotification: Notification = {
        ...notification,
        id: `notification_${Date.now()}`,
        isRead: false,
        createdAt: new Date().toISOString(),
      };

      setNotifications(prev => [newNotification, ...prev]);

      // Send push notification if enabled
      if (notification.isPush && permissions.granted) {
        // This would use platform-specific push notification service
        console.log('Sending push notification:', newNotification.title);
      }

      // Save to backend
      await api.execute('/api/notifications', {
        method: 'POST',
        body: newNotification,
      });

      return newNotification;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send notification';
      setError(errorMessage);
      errorService.logCriticalError('Failed to send notification', err as Error, {
        action: 'useNotifications.sendNotification',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api, permissions.granted]);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, isRead: true }
            : notification
        )
      );

      await api.execute(`/api/notifications/${notificationId}/read`, {
        method: 'PUT',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to mark notification as read';
      setError(errorMessage);
      errorService.logCriticalError('Failed to mark notification as read', err as Error, {
        action: 'useNotifications.markAsRead',
      });
      throw err;
    }
  }, [api]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, isRead: true }))
      );

      await api.execute('/api/notifications/read-all', {
        method: 'PUT',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to mark all notifications as read';
      setError(errorMessage);
      errorService.logCriticalError('Failed to mark all notifications as read', err as Error, {
        action: 'useNotifications.markAllAsRead',
      });
      throw err;
    }
  }, [api]);

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      setNotifications(prev => prev.filter(notification => notification.id !== notificationId));

      await api.execute(`/api/notifications/${notificationId}`, {
        method: 'DELETE',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete notification';
      setError(errorMessage);
      errorService.logCriticalError('Failed to delete notification', err as Error, {
        action: 'useNotifications.deleteNotification',
      });
      throw err;
    }
  }, [api]);

  // Clear all notifications
  const clearAllNotifications = useCallback(async () => {
    try {
      setNotifications([]);

      await api.execute('/api/notifications/clear', {
        method: 'DELETE',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear notifications';
      setError(errorMessage);
      errorService.logCriticalError('Failed to clear all notifications', err as Error, {
        action: 'useNotifications.clearAllNotifications',
      });
      throw err;
    }
  }, [api]);

  // Get notifications from server
  const fetchNotifications = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await api.execute('/api/notifications');
      const fetchedNotifications = response.data || [];
      
      setNotifications(fetchedNotifications);
      return fetchedNotifications;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';
      setError(errorMessage);
      errorService.logCriticalError('Failed to fetch notifications', err as Error, {
        action: 'useNotifications.fetchNotifications',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [api]);

  // Check if in quiet hours
  const isInQuietHours = useCallback(() => {
    if (!settings.quietHours.enabled) return false;

    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const { startTime, endTime } = settings.quietHours;
    
    // Handle overnight quiet hours (e.g., 22:00 to 08:00)
    if (startTime > endTime) {
      return currentTime >= startTime || currentTime <= endTime;
    }
    
    // Handle same-day quiet hours (e.g., 12:00 to 14:00)
    return currentTime >= startTime && currentTime <= endTime;
  }, [settings.quietHours]);

  // Auto-request permissions on mount
  useEffect(() => {
    if (autoRequestPermissions && !permissions.granted && !permissions.denied) {
      requestPermissions();
    }
  }, [autoRequestPermissions, permissions.granted, permissions.denied, requestPermissions]);

  // Computed values
  const unreadCount = notifications.filter(n => !n.isRead).length;
  const unreadNotifications = notifications.filter(n => !n.isRead);
  const readNotifications = notifications.filter(n => n.isRead);
  const activeScheduledNotifications = scheduledNotifications.filter(n => n.isActive);

  return {
    // State
    notifications,
    scheduledNotifications,
    settings,
    permissions,
    pushToken,
    unreadCount,
    unreadNotifications,
    readNotifications,
    activeScheduledNotifications,
    isLoading,
    error,

    // Actions
    requestPermissions,
    updateSettings,
    scheduleNotification,
    cancelScheduledNotification,
    sendNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications,
    fetchNotifications,

    // Utilities
    isInQuietHours,
    clearError: () => setError(null),
  };
};

/**
 * Hook for workout reminder notifications
 */
export const useWorkoutReminders = () => {
  const { scheduleNotification, cancelScheduledNotification, settings } = useNotifications();

  const scheduleWorkoutReminder = useCallback(async (
    workoutTime: string, // ISO string
    workoutName: string,
    reminderMinutes: number = 30
  ) => {
    if (!settings.workoutReminders) return null;

    const reminderTime = new Date(new Date(workoutTime).getTime() - reminderMinutes * 60 * 1000);
    
    return await scheduleNotification({
      type: 'workout',
      title: 'Workout Reminder',
      message: `Your ${workoutName} workout starts in ${reminderMinutes} minutes!`,
      scheduledFor: reminderTime.toISOString(),
      data: { workoutName, originalTime: workoutTime },
    });
  }, [scheduleNotification, settings.workoutReminders]);

  const scheduleDailyWorkoutReminder = useCallback(async (
    time: string, // HH:MM format
    message: string = 'Time for your daily workout!'
  ) => {
    if (!settings.workoutReminders) return null;

    const [hours, minutes] = time.split(':').map(Number);
    const scheduledTime = new Date();
    scheduledTime.setHours(hours, minutes, 0, 0);
    
    // If time has passed today, schedule for tomorrow
    if (scheduledTime.getTime() <= Date.now()) {
      scheduledTime.setDate(scheduledTime.getDate() + 1);
    }

    return await scheduleNotification({
      type: 'workout',
      title: 'Daily Workout Reminder',
      message,
      scheduledFor: scheduledTime.toISOString(),
      repeatInterval: 'daily',
    });
  }, [scheduleNotification, settings.workoutReminders]);

  return {
    scheduleWorkoutReminder,
    scheduleDailyWorkoutReminder,
    cancelScheduledNotification,
  };
};

/**
 * Hook for meal reminder notifications
 */
export const useMealReminders = () => {
  const { scheduleNotification, settings } = useNotifications();

  const scheduleMealReminder = useCallback(async (
    mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack',
    time: string // HH:MM format
  ) => {
    if (!settings.mealReminders) return null;

    const [hours, minutes] = time.split(':').map(Number);
    const scheduledTime = new Date();
    scheduledTime.setHours(hours, minutes, 0, 0);
    
    // If time has passed today, schedule for tomorrow
    if (scheduledTime.getTime() <= Date.now()) {
      scheduledTime.setDate(scheduledTime.getDate() + 1);
    }

    const mealNames = {
      breakfast: 'Breakfast',
      lunch: 'Lunch', 
      dinner: 'Dinner',
      snack: 'Snack',
    };

    return await scheduleNotification({
      type: 'meal',
      title: `${mealNames[mealType]} Reminder`,
      message: `Time for ${mealNames[mealType].toLowerCase()}! Don't forget to log your meal.`,
      scheduledFor: scheduledTime.toISOString(),
      repeatInterval: 'daily',
      data: { mealType },
    });
  }, [scheduleNotification, settings.mealReminders]);

  return {
    scheduleMealReminder,
  };
};