import type { StackNavigationProp } from '@react-navigation/stack';
import type { RouteProp } from '@react-navigation/native';
import { WorkoutPlan } from './workout';

// Root Stack Navigator
export type RootStackParamList = {
  // Auth Stack
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  
  // Onboarding Stack
  Onboarding: undefined;
  PersonalInfo: undefined;
  FitnessGoals: undefined;
  ActivityLevel: undefined;
  
  // Main App
  MainTabs: undefined;
  
  // Workout Stack
  WorkoutDetail: { workoutId: string };
  WorkoutSession: { workoutPlan: WorkoutPlan };
  ExerciseDetail: { exerciseId: string };
  WorkoutHistory: undefined;
  CreateWorkout: undefined;
  EditWorkout: { workoutId: string };
  
  // Nutrition Stack
  RecipeDetail: { recipeId: string };
  MealPlanDetail: { mealPlanId: string };
  FoodSearch: undefined;
  LogMeal: { mealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack' };
  NutritionHistory: undefined;
  
  // Progress Stack
  ProgressDetail: { entryId: string };
  AddProgress: undefined;
  ProgressPhotos: undefined;
  GoalDetail: { goalId: string };
  CreateGoal: undefined;
  
  // Profile Stack
  EditProfile: undefined;
  Settings: undefined;
  Notifications: undefined;
  Privacy: undefined;
  About: undefined;
};

// Bottom Tab Navigator
export type MainTabParamList = {
  Home: undefined;
  Workouts: undefined;
  Nutrition: undefined;
  Progress: undefined;
  Profile: undefined;
};

// Auth Stack Navigator
export type AuthStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

// Onboarding Stack Navigator
export type OnboardingStackParamList = {
  PersonalInfo: undefined;
  FitnessGoals: undefined;
  ActivityLevel: undefined;
};

// Workout Stack Navigator
export type WorkoutStackParamList = {
  WorkoutList: undefined;
  WorkoutDetail: { workoutId: string };
  WorkoutSession: { workoutPlan: WorkoutPlan };
  ExerciseDetail: { exerciseId: string };
  WorkoutHistory: undefined;
  CreateWorkout: undefined;
  EditWorkout: { workoutId: string };
};

// Nutrition Stack Navigator
export type NutritionStackParamList = {
  NutritionDashboard: undefined;
  RecipeDetail: { recipeId: string };
  MealPlanDetail: { mealPlanId: string };
  FoodSearch: undefined;
  LogMeal: { mealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack' };
  NutritionHistory: undefined;
};

// Progress Stack Navigator
export type ProgressStackParamList = {
  ProgressDashboard: undefined;
  ProgressDetail: { entryId: string };
  AddProgress: undefined;
  ProgressPhotos: undefined;
  GoalDetail: { goalId: string };
  CreateGoal: undefined;
};

// Profile Stack Navigator
export type ProfileStackParamList = {
  ProfileMain: undefined;
  EditProfile: undefined;
  Settings: undefined;
  Notifications: undefined;
  Privacy: undefined;
  About: undefined;
};

// Navigation Props
export type RootStackNavigationProp<T extends keyof RootStackParamList> = StackNavigationProp<RootStackParamList, T>;
export type MainTabNavigationProp<T extends keyof MainTabParamList> = StackNavigationProp<MainTabParamList, T>;
export type AuthStackNavigationProp<T extends keyof AuthStackParamList> = StackNavigationProp<AuthStackParamList, T>;
export type OnboardingStackNavigationProp<T extends keyof OnboardingStackParamList> = StackNavigationProp<OnboardingStackParamList, T>;
export type WorkoutStackNavigationProp<T extends keyof WorkoutStackParamList> = StackNavigationProp<WorkoutStackParamList, T>;
export type NutritionStackNavigationProp<T extends keyof NutritionStackParamList> = StackNavigationProp<NutritionStackParamList, T>;
export type ProgressStackNavigationProp<T extends keyof ProgressStackParamList> = StackNavigationProp<ProgressStackParamList, T>;
export type ProfileStackNavigationProp<T extends keyof ProfileStackParamList> = StackNavigationProp<ProfileStackParamList, T>;

// Route Props
export type RootStackRouteProp<T extends keyof RootStackParamList> = RouteProp<RootStackParamList, T>;
export type MainTabRouteProp<T extends keyof MainTabParamList> = RouteProp<MainTabParamList, T>;
export type AuthStackRouteProp<T extends keyof AuthStackParamList> = RouteProp<AuthStackParamList, T>;
export type OnboardingStackRouteProp<T extends keyof OnboardingStackParamList> = RouteProp<OnboardingStackParamList, T>;
export type WorkoutStackRouteProp<T extends keyof WorkoutStackParamList> = RouteProp<WorkoutStackParamList, T>;
export type NutritionStackRouteProp<T extends keyof NutritionStackParamList> = RouteProp<NutritionStackParamList, T>;
export type ProgressStackRouteProp<T extends keyof ProgressStackParamList> = RouteProp<ProgressStackParamList, T>;
export type ProfileStackRouteProp<T extends keyof ProfileStackParamList> = RouteProp<ProfileStackParamList, T>;

// Combined Navigation and Route Props
export type ScreenProps<
  TParamList extends Record<string, object | undefined>,
  TRouteName extends keyof TParamList
> = {
  navigation: StackNavigationProp<TParamList, TRouteName>;
  route: RouteProp<TParamList, TRouteName>;
};

// Screen Component Props
export type RootStackScreenProps<T extends keyof RootStackParamList> = ScreenProps<RootStackParamList, T>;
export type MainTabScreenProps<T extends keyof MainTabParamList> = ScreenProps<MainTabParamList, T>;
export type AuthStackScreenProps<T extends keyof AuthStackParamList> = ScreenProps<AuthStackParamList, T>;
export type OnboardingStackScreenProps<T extends keyof OnboardingStackParamList> = ScreenProps<OnboardingStackParamList, T>;
export type WorkoutStackScreenProps<T extends keyof WorkoutStackParamList> = ScreenProps<WorkoutStackParamList, T>;
export type NutritionStackScreenProps<T extends keyof NutritionStackParamList> = ScreenProps<NutritionStackParamList, T>;
export type ProgressStackScreenProps<T extends keyof ProgressStackParamList> = ScreenProps<ProgressStackParamList, T>;
export type ProfileStackScreenProps<T extends keyof ProfileStackParamList> = ScreenProps<ProfileStackParamList, T>;

// Navigation State
export interface NavigationState {
  isLoading: boolean;
  currentRoute: string;
  previousRoute?: string;
  canGoBack: boolean;
}

// Deep Linking
export interface DeepLinkConfig {
  screens: {
    [key in keyof RootStackParamList]: string;
  };
}

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}