export interface NutritionInfo {
  calories: number;
  protein: number; // in grams
  carbohydrates: number; // in grams
  fat: number; // in grams
  fiber?: number; // in grams
  sugar?: number; // in grams
  sodium?: number; // in mg
  cholesterol?: number; // in mg
}

export interface Ingredient {
  id: string;
  name: string;
  quantity: number;
  unit: string; // 'g', 'ml', 'cup', 'tbsp', etc.
  nutritionPer100g: NutritionInfo;
}

export interface Recipe {
  id: string;
  name: string;
  description: string;
  instructions: string[];
  ingredients: Ingredient[];
  prepTime: number; // in minutes
  cookTime: number; // in minutes
  servings: number;
  difficulty: 'easy' | 'medium' | 'hard';
  tags: string[];
  imageUrl?: string;
  nutrition: NutritionInfo;
}

export interface Meal {
  id: string;
  name: string;
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  recipes: Recipe[];
  totalNutrition: NutritionInfo;
  estimatedTime: number; // total prep + cook time
}

export interface DayMealPlan {
  id: string;
  date: Date;
  meals: Meal[];
  totalNutrition: NutritionInfo;
  targetCalories: number;
  waterIntake?: number; // in ml
  notes?: string;
}

export interface MealPlan {
  id: string;
  name: string;
  description: string;
  targetGender: 'male' | 'female' | 'unisex';
  fitnessGoal: 'weight_loss' | 'muscle_gain' | 'maintenance' | 'endurance';
  duration: number; // in days
  dailyMealPlans: DayMealPlan[];
  averageDailyNutrition: NutritionInfo;
  shoppingList?: ShoppingItem[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ShoppingItem {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  category: 'produce' | 'meat' | 'dairy' | 'pantry' | 'frozen' | 'other';
  isPurchased: boolean;
}

export interface FoodLog {
  id: string;
  userId: string;
  date: Date;
  meals: LoggedMeal[];
  totalNutrition: NutritionInfo;
  waterIntake: number;
  notes?: string;
}

export interface LoggedMeal {
  id: string;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  foods: LoggedFood[];
  totalNutrition: NutritionInfo;
  timestamp: Date;
}

export interface LoggedFood {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  nutrition: NutritionInfo;
  recipeId?: string;
}