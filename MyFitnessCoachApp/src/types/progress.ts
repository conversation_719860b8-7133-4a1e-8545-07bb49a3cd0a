export interface ProgressEntry {
  id: string;
  userId: string;
  date: Date;
  weight?: number; // in kg
  bodyFatPercentage?: number;
  muscleMass?: number; // in kg
  measurements?: BodyMeasurements;
  photos?: ProgressPhoto[];
  notes?: string;
  mood?: 1 | 2 | 3 | 4 | 5; // 1 = very bad, 5 = excellent
  energyLevel?: 1 | 2 | 3 | 4 | 5;
  sleepQuality?: 1 | 2 | 3 | 4 | 5;
  createdAt: Date;
}

export interface BodyMeasurements {
  chest?: number; // in cm
  waist?: number;
  hips?: number;
  thighs?: number;
  arms?: number;
  neck?: number;
  shoulders?: number;
}

export interface ProgressPhoto {
  id: string;
  uri: string;
  type: 'front' | 'side' | 'back';
  timestamp: Date;
}

export interface WorkoutProgress {
  id: string;
  userId: string;
  exerciseId: string;
  exerciseName: string;
  date: Date;
  bestSet: {
    weight?: number;
    reps?: number;
    duration?: number;
  };
  totalVolume?: number; // weight * reps * sets
  personalRecord: boolean;
  notes?: string;
}

export interface ProgressStats {
  totalWorkouts: number;
  totalWorkoutTime: number; // in minutes
  averageWorkoutDuration: number;
  currentStreak: number; // consecutive days
  longestStreak: number;
  totalCaloriesBurned: number;
  weightChange: number; // positive = gained, negative = lost
  strengthGains: StrengthGain[];
  consistencyScore: number; // 0-100
}

export interface StrengthGain {
  exerciseId: string;
  exerciseName: string;
  startingWeight: number;
  currentWeight: number;
  improvement: number; // percentage
  lastUpdated: Date;
}

export interface Goal {
  id: string;
  userId: string;
  type: 'weight' | 'strength' | 'endurance' | 'habit' | 'custom';
  title: string;
  description: string;
  targetValue: number;
  currentValue: number;
  unit: string;
  targetDate: Date;
  isCompleted: boolean;
  completedAt?: Date;
  priority: 'low' | 'medium' | 'high';
  createdAt: Date;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  iconUrl: string;
  category: 'workout' | 'nutrition' | 'consistency' | 'strength' | 'milestone';
  requirement: {
    type: string;
    value: number;
    unit?: string;
  };
  unlockedAt?: Date;
  isUnlocked: boolean;
}

export interface ProgressSummary {
  period: 'week' | 'month' | 'quarter' | 'year';
  startDate: Date;
  endDate: Date;
  workoutsSummary: {
    completed: number;
    planned: number;
    totalDuration: number;
    averageDuration: number;
  };
  nutritionSummary: {
    averageCalories: number;
    targetCalories: number;
    adherencePercentage: number;
  };
  progressSummary: {
    weightChange: number;
    strengthGains: number;
    newPersonalRecords: number;
  };
  achievements: Achievement[];
}