export interface Exercise {
  id: string;
  name: string;
  description: string;
  instructions: string[];
  targetMuscles: string[];
  equipment: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: 'strength' | 'cardio' | 'flexibility' | 'balance';
  mediaUrl?: string; // URL to exercise demonstration video/gif
  thumbnailUrl?: string;
  duration?: number; // in seconds for timed exercises
  restTime?: number; // recommended rest time in seconds
  tips: string[];
  variations?: ExerciseVariation[];
}

export interface ExerciseVariation {
  id: string;
  name: string;
  description: string;
  difficulty: 'easier' | 'harder';
  modifications: string[];
}

export interface WorkoutExercise {
  exerciseId: string;
  exercise: Exercise;
  sets: number;
  reps?: number;
  duration?: number; // in seconds
  weight?: number; // in kg
  restTime: number; // in seconds
  notes?: string;
  order: number;
}

export interface WorkoutPlan {
  id: string;
  name: string;
  description: string;
  targetGender: 'male' | 'female' | 'unisex';
  fitnessGoal: 'weight_loss' | 'muscle_gain' | 'maintenance' | 'endurance';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // total workout duration in minutes
  exercises: WorkoutExercise[];
  equipment: string[];
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkoutSchedule {
  id: string;
  userId: string;
  workoutPlanId: string;
  workoutPlan: WorkoutPlan;
  scheduledDate: Date;
  isCompleted: boolean;
  completedAt?: Date;
  actualDuration?: number;
  notes?: string;
  exerciseResults?: ExerciseResult[];
}

export interface ExerciseResult {
  exerciseId: string;
  completedSets: number;
  actualReps?: number[];
  actualWeight?: number[];
  actualDuration?: number;
  skipped: boolean;
  notes?: string;
}

export interface WorkoutSession {
  id: string;
  workoutPlanId: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  isActive: boolean;
  currentExerciseIndex: number;
  currentSet: number;
  exerciseResults: ExerciseResult[];
  totalCaloriesBurned?: number;
}