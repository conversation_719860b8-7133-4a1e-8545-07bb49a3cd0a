import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MainTabScreenProps } from '../../types/navigation';
import { useUserStore } from '../../stores/userStore';
import { theme } from '../../theme';

type HomeScreenProps = MainTabScreenProps<'Home'>;

const HomeScreen: React.FC<HomeScreenProps> = () => {
  const { user, logout } = useUserStore();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Welcome back, {user?.name || 'User'}!</Text>
      <Text style={styles.subtitle}>Your fitness journey continues here</Text>
      
      <TouchableOpacity style={styles.logoutButton} onPress={logout}>
        <Text style={styles.logoutButtonText}>Logout</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing[6],
  },
  title: {
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.bold as any,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing[4],
    textAlign: 'center',
  },
  subtitle: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing[8],
    textAlign: 'center',
  },
  logoutButton: {
    backgroundColor: theme.colors.error,
    paddingHorizontal: theme.spacing[6],
    paddingVertical: theme.spacing[3],
    borderRadius: theme.borderRadius.base,
  },
  logoutButtonText: {
    color: theme.colors.text.inverse,
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold as any,
  },
});

export default HomeScreen;