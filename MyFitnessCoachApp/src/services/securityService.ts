// Simple encryption key - in production, this should be more secure
const ENCRYPTION_KEY = 'MyFitnessCoachApp2024SecretKey';

class SecurityService {
  /**
   * Simple encoding for basic data obfuscation
   * Note: This is not true encryption, just basic obfuscation
   */
  async encrypt(data: string): Promise<string> {
    try {
      // Simple character shifting with key
      const keySum = ENCRYPTION_KEY.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
      const shift = keySum % 26;
      
      const encoded = data.split('').map((char: string) => {
        const code = char.charCodeAt(0);
        return String.fromCharCode(code + shift);
      }).join('');
      
      // Simple hex encoding instead of base64
      return encoded.split('').map((char: string) => char.charCodeAt(0).toString(16)).join('');
    } catch (error) {
      console.error('Encryption failed:', error);
      // Return original data if encryption fails to prevent data loss
      return data;
    }
  }

  /**
   * Simple decoding
   */
  async decrypt(encryptedData: string): Promise<string> {
    try {
      // Decode from hex
      const hexPairs = encryptedData.match(/.{1,2}/g) || [];
      const decoded = hexPairs.map((hex: string) => String.fromCharCode(parseInt(hex, 16))).join('');
      
      // Reverse the character shifting
      const keySum = ENCRYPTION_KEY.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
      const shift = keySum % 26;
      
      const originalData = decoded.split('').map((char: string) => {
        const code = char.charCodeAt(0);
        return String.fromCharCode(code - shift);
      }).join('');
      
      return originalData;
    } catch (error) {
      console.error('Decryption failed:', error);
      // Return original data if decryption fails
      return encryptedData;
    }
  }

  /**
   * Generate a simple hash for data integrity
   */
  generateHash(data: string): string {
    try {
      // Simple hash function
      let hash = 0;
      for (let i = 0; i < data.length; i++) {
        const char = data.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
      }
      return hash.toString();
    } catch (error) {
      console.error('Hash generation failed:', error);
      return '';
    }
  }

  /**
   * Verify data integrity using hash
   */
  verifyHash(data: string, hash: string): boolean {
    try {
      const generatedHash = this.generateHash(data);
      return generatedHash === hash;
    } catch (error) {
      console.error('Hash verification failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const securityService = new SecurityService();