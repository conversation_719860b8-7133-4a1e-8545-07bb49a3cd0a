import { storageService } from './storageService';

export enum ErrorType {
  NETWORK = 'NETWORK',
  STORAGE = 'STORAGE',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  PERMISSION = 'PERMISSION',
  UNKNOWN = 'UNKNOWN',
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export interface AppError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: any;
  timestamp: Date;
  userId?: string;
  context?: {
    screen?: string;
    action?: string;
    component?: string;
    additionalData?: Record<string, any>;
  };
  stack?: string;
  resolved: boolean;
}

export interface ErrorReport {
  errors: AppError[];
  deviceInfo: {
    platform: string;
    version: string;
    model?: string;
  };
  appVersion: string;
  timestamp: Date;
}

class ErrorService {
  private errors: AppError[] = [];
  private maxStoredErrors = 100;
  private errorListeners: ((error: AppError) => void)[] = [];

  constructor() {
    this.loadStoredErrors();
    this.setupGlobalErrorHandlers();
  }

  // Create and log an error
  async logError(
    type: ErrorType,
    message: string,
    options: {
      severity?: ErrorSeverity;
      details?: any;
      userId?: string;
      context?: AppError['context'];
      originalError?: Error;
    } = {}
  ): Promise<AppError> {
    const error: AppError = {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity: options.severity || ErrorSeverity.MEDIUM,
      message,
      details: options.details,
      timestamp: new Date(),
      userId: options.userId,
      context: options.context,
      stack: options.originalError?.stack,
      resolved: false,
    };

    this.errors.unshift(error);
    
    // Keep only the most recent errors
    if (this.errors.length > this.maxStoredErrors) {
      this.errors = this.errors.slice(0, this.maxStoredErrors);
    }

    // Store errors persistently
    await this.saveErrors();

    // Notify listeners
    this.notifyListeners(error);

    // Log to console in development
    if (__DEV__) {
      console.error('AppError:', error);
      if (options.originalError) {
        console.error('Original Error:', options.originalError);
      }
    }

    return error;
  }

  // Convenience methods for different error types
  async logNetworkError(
    message: string,
    details?: any,
    context?: AppError['context']
  ): Promise<AppError> {
    return this.logError(ErrorType.NETWORK, message, {
      severity: ErrorSeverity.MEDIUM,
      details,
      context,
    });
  }

  async logStorageError(
    message: string,
    details?: any,
    context?: AppError['context']
  ): Promise<AppError> {
    return this.logError(ErrorType.STORAGE, message, {
      severity: ErrorSeverity.HIGH,
      details,
      context,
    });
  }

  async logValidationError(
    message: string,
    details?: any,
    context?: AppError['context']
  ): Promise<AppError> {
    return this.logError(ErrorType.VALIDATION, message, {
      severity: ErrorSeverity.LOW,
      details,
      context,
    });
  }

  async logAuthError(
    message: string,
    details?: any,
    context?: AppError['context']
  ): Promise<AppError> {
    return this.logError(ErrorType.AUTHENTICATION, message, {
      severity: ErrorSeverity.HIGH,
      details,
      context,
    });
  }

  async logCriticalError(
    message: string,
    originalError?: Error,
    context?: AppError['context']
  ): Promise<AppError> {
    return this.logError(ErrorType.UNKNOWN, message, {
      severity: ErrorSeverity.CRITICAL,
      originalError,
      context,
    });
  }

  // Handle JavaScript errors
  handleJSError = (error: Error, isFatal: boolean = false): void => {
    this.logError(
      ErrorType.UNKNOWN,
      error.message || 'Unknown JavaScript error',
      {
        severity: isFatal ? ErrorSeverity.CRITICAL : ErrorSeverity.HIGH,
        originalError: error,
        context: {
          action: 'javascript_error',
          additionalData: { isFatal },
        },
      }
    );
  };

  // Handle promise rejections
  handlePromiseRejection = (reason: any): void => {
    const message = reason?.message || reason?.toString() || 'Unhandled promise rejection';
    this.logError(ErrorType.UNKNOWN, message, {
      severity: ErrorSeverity.MEDIUM,
      details: reason,
      context: {
        action: 'promise_rejection',
      },
    });
  };

  // Get errors with filtering
  getErrors(filters?: {
    type?: ErrorType;
    severity?: ErrorSeverity;
    resolved?: boolean;
    userId?: string;
    limit?: number;
  }): AppError[] {
    let filteredErrors = [...this.errors];

    if (filters?.type) {
      filteredErrors = filteredErrors.filter(error => error.type === filters.type);
    }

    if (filters?.severity) {
      filteredErrors = filteredErrors.filter(error => error.severity === filters.severity);
    }

    if (filters?.resolved !== undefined) {
      filteredErrors = filteredErrors.filter(error => error.resolved === filters.resolved);
    }

    if (filters?.userId) {
      filteredErrors = filteredErrors.filter(error => error.userId === filters.userId);
    }

    if (filters?.limit) {
      filteredErrors = filteredErrors.slice(0, filters.limit);
    }

    return filteredErrors;
  }

  // Mark error as resolved
  async resolveError(errorId: string): Promise<boolean> {
    const errorIndex = this.errors.findIndex(error => error.id === errorId);
    if (errorIndex !== -1) {
      this.errors[errorIndex].resolved = true;
      await this.saveErrors();
      return true;
    }
    return false;
  }

  // Clear errors
  async clearErrors(filters?: {
    type?: ErrorType;
    severity?: ErrorSeverity;
    resolved?: boolean;
    olderThan?: Date;
  }): Promise<number> {
    const initialCount = this.errors.length;

    if (!filters) {
      this.errors = [];
    } else {
      this.errors = this.errors.filter(error => {
        if (filters.type && error.type === filters.type) return false;
        if (filters.severity && error.severity === filters.severity) return false;
        if (filters.resolved !== undefined && error.resolved === filters.resolved) return false;
        if (filters.olderThan && error.timestamp < filters.olderThan) return false;
        return true;
      });
    }

    await this.saveErrors();
    return initialCount - this.errors.length;
  }

  // Generate error report
  generateErrorReport(): ErrorReport {
    return {
      errors: this.getErrors({ limit: 50 }),
      deviceInfo: {
        platform: 'react-native',
        version: '0.72.0', // This should be dynamic
      },
      appVersion: '1.0.0', // This should come from app config
      timestamp: new Date(),
    };
  }

  // Subscribe to error events
  onError(callback: (error: AppError) => void): () => void {
    this.errorListeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.errorListeners.indexOf(callback);
      if (index > -1) {
        this.errorListeners.splice(index, 1);
      }
    };
  }

  // Get error statistics
  getErrorStats(): {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
    resolved: number;
    unresolved: number;
  } {
    const stats = {
      total: this.errors.length,
      byType: {} as Record<ErrorType, number>,
      bySeverity: {} as Record<ErrorSeverity, number>,
      resolved: 0,
      unresolved: 0,
    };

    // Initialize counters
    Object.values(ErrorType).forEach(type => {
      stats.byType[type] = 0;
    });
    Object.values(ErrorSeverity).forEach(severity => {
      stats.bySeverity[severity] = 0;
    });

    // Count errors
    this.errors.forEach(error => {
      stats.byType[error.type]++;
      stats.bySeverity[error.severity]++;
      if (error.resolved) {
        stats.resolved++;
      } else {
        stats.unresolved++;
      }
    });

    return stats;
  }

  // Private methods
  private async loadStoredErrors(): Promise<void> {
    try {
      const storedErrors = await storageService.getItem<AppError[]>('app_errors');
      if (storedErrors && Array.isArray(storedErrors)) {
        this.errors = storedErrors.map(error => ({
          ...error,
          timestamp: new Date(error.timestamp),
        }));
      }
    } catch (error) {
      console.warn('Failed to load stored errors:', error);
    }
  }

  private async saveErrors(): Promise<void> {
    try {
      await storageService.setItem('app_errors', this.errors);
    } catch (error) {
      console.warn('Failed to save errors:', error);
    }
  }

  private notifyListeners(error: AppError): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(error);
      } catch (err) {
        console.warn('Error in error listener:', err);
      }
    });
  }

  private setupGlobalErrorHandlers(): void {
    // Global error handlers would be set up here in a real React Native app
    // For React Native, you would use:
    // - ErrorUtils.setGlobalHandler for JS errors
    // - require('react-native').LogBox for development
    // - Crashlytics or similar for production crash reporting
    
    // For now, we'll rely on manual error logging throughout the app
    // This is a placeholder for future implementation
  }
}

// Export singleton instance
export const errorService = new ErrorService();

// Utility function to wrap async operations with error handling
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  context?: AppError['context'],
  errorType: ErrorType = ErrorType.UNKNOWN
): Promise<T | null> {
  try {
    return await operation();
  } catch (error) {
    await errorService.logError(
      errorType,
      error instanceof Error ? error.message : 'Unknown error',
      {
        originalError: error instanceof Error ? error : undefined,
        context,
      }
    );
    return null;
  }
}

// Utility function to wrap sync operations with error handling
export function withSyncErrorHandling<T>(
  operation: () => T,
  context?: AppError['context'],
  errorType: ErrorType = ErrorType.UNKNOWN
): T | null {
  try {
    return operation();
  } catch (error) {
    errorService.logError(
      errorType,
      error instanceof Error ? error.message : 'Unknown error',
      {
        originalError: error instanceof Error ? error : undefined,
        context,
      }
    );
    return null;
  }
}