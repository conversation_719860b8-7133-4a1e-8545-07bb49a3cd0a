import { <PERSON><PERSON><PERSON><PERSON> } from 'react-native-mmkv';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { securityService } from './securityService';

// MMKV instance for fast storage
const storage = new MMKV();

// Storage keys
export const STORAGE_KEYS = {
  USER_PROFILE: 'user_profile',
  USER_PREFERENCES: 'user_preferences',
  WORKOUT_PROGRESS: 'workout_progress',
  NUTRITION_LOG: 'nutrition_log',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  THEME_PREFERENCE: 'theme_preference',
  LAST_SYNC: 'last_sync',
} as const;

// Sensitive data keys that require encryption
const SENSITIVE_KEYS = [
  STORAGE_KEYS.USER_PROFILE,
  STORAGE_KEYS.NUTRITION_LOG,
] as const;

type SensitiveKey = typeof SENSITIVE_KEYS[number];

export interface StorageOptions {
  encrypt?: boolean;
  useAsyncStorage?: boolean; // For large data that doesn't need fast access
}

class StorageService {
  /**
   * Store data with optional encryption
   */
  async setItem<T>(
    key: string,
    value: T,
    options: StorageOptions = {}
  ): Promise<void> {
    try {
      const { encrypt = SENSITIVE_KEYS.includes(key as SensitiveKey), useAsyncStorage = false } = options;
      
      let serializedValue = JSON.stringify(value);
      
      // Encrypt sensitive data
      if (encrypt) {
        serializedValue = await securityService.encrypt(serializedValue);
      }
      
      // Use AsyncStorage for large data, MMKV for fast access
      if (useAsyncStorage) {
        await AsyncStorage.setItem(key, serializedValue);
      } else {
        storage.set(key, serializedValue);
      }
    } catch (error) {
      console.error(`Failed to store data for key: ${key}`, error);
      throw new Error(`Storage write failed: ${error}`);
    }
  }

  /**
   * Retrieve data with automatic decryption
   */
  async getItem<T>(
    key: string,
    options: StorageOptions = {}
  ): Promise<T | null> {
    try {
      const { encrypt = SENSITIVE_KEYS.includes(key as SensitiveKey), useAsyncStorage = false } = options;
      
      let serializedValue: string | null;
      
      // Retrieve from appropriate storage
      if (useAsyncStorage) {
        serializedValue = await AsyncStorage.getItem(key);
      } else {
        serializedValue = storage.getString(key) || null;
      }
      
      if (!serializedValue) {
        return null;
      }
      
      // Decrypt if necessary
      if (encrypt && serializedValue) {
        serializedValue = await securityService.decrypt(serializedValue);
      }
      
      if (!serializedValue) {
        return null;
      }
      
      return JSON.parse(serializedValue) as T;
    } catch (error) {
      console.error(`Failed to retrieve data for key: ${key}`, error);
      // Return null instead of throwing to allow graceful fallbacks
      return null;
    }
  }

  /**
   * Remove item from storage
   */
  async removeItem(key: string, useAsyncStorage = false): Promise<void> {
    try {
      if (useAsyncStorage) {
        await AsyncStorage.removeItem(key);
      } else {
        storage.delete(key);
      }
    } catch (error) {
      console.error(`Failed to remove data for key: ${key}`, error);
      throw new Error(`Storage delete failed: ${error}`);
    }
  }

  /**
   * Clear all data from storage
   */
  async clearAll(): Promise<void> {
    try {
      storage.clearAll();
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Failed to clear storage', error);
      throw new Error(`Storage clear failed: ${error}`);
    }
  }

  /**
   * Get all keys from MMKV storage
   */
  getAllKeys(): string[] {
    try {
      return storage.getAllKeys();
    } catch (error) {
      console.error('Failed to get all keys', error);
      return [];
    }
  }

  /**
   * Check if a key exists in storage
   */
  async hasItem(key: string, useAsyncStorage = false): Promise<boolean> {
    try {
      if (useAsyncStorage) {
        const value = await AsyncStorage.getItem(key);
        return value !== null;
      } else {
        return storage.contains(key);
      }
    } catch (error) {
      console.error(`Failed to check existence for key: ${key}`, error);
      return false;
    }
  }

  /**
   * Get storage size information
   */
  getStorageInfo(): { mmkvSize: number; keys: string[] } {
    try {
      const keys = this.getAllKeys();
      // MMKV doesn't provide direct size info, so we estimate
      const mmkvSize = keys.length;
      
      return {
        mmkvSize,
        keys,
      };
    } catch (error) {
      console.error('Failed to get storage info', error);
      return { mmkvSize: 0, keys: [] };
    }
  }

  /**
   * Batch operations for better performance
   */
  async batchSet(items: Array<{ key: string; value: any; options?: StorageOptions }>): Promise<void> {
    try {
      const promises = items.map(({ key, value, options }) => 
        this.setItem(key, value, options)
      );
      await Promise.all(promises);
    } catch (error) {
      console.error('Batch set operation failed', error);
      throw new Error(`Batch storage write failed: ${error}`);
    }
  }

  /**
   * Batch get operations
   */
  async batchGet<T>(keys: Array<{ key: string; options?: StorageOptions }>): Promise<Record<string, T | null>> {
    try {
      const promises = keys.map(async ({ key, options }) => ({
        key,
        value: await this.getItem<T>(key, options),
      }));
      
      const results = await Promise.all(promises);
      
      return results.reduce((acc, { key, value }) => {
        acc[key] = value;
        return acc;
      }, {} as Record<string, T | null>);
    } catch (error) {
      console.error('Batch get operation failed', error);
      return {};
    }
  }
}

// Export singleton instance
export const storageService = new StorageService();