import { UserProfile } from '../types/user';
import { WorkoutPlan, Exercise, WorkoutSession } from '../types/workout';
import { MealPlan, FoodLog } from '../types/nutrition';
import { ProgressEntry } from '../types/progress';

// Simulate API response delays
const API_DELAY = {
  FAST: 200,
  MEDIUM: 500,
  SLOW: 1000,
};

// Simulate network errors occasionally
const ERROR_RATE = 0.05; // 5% chance of error

class MockApiService {
  private simulateDelay(type: keyof typeof API_DELAY = 'MEDIUM'): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, API_DELAY[type]);
    });
  }

  private simulateError(): void {
    if (Math.random() < ERROR_RATE) {
      throw new Error('Network error: Unable to connect to server');
    }
  }

  // User Profile Services
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    await this.simulateDelay('FAST');
    this.simulateError();

    // Return null for new users
    if (userId === 'new_user') {
      return null;
    }

    // Mock user profile
    return {
      id: userId,
      name: '<PERSON>',
      email: '<EMAIL>',
      age: 28,
      gender: 'male',
      height: 175, // cm
      weight: 75, // kg
      fitnessGoal: 'muscle_gain',
      activityLevel: 'moderately_active',
      preferences: {
        units: 'metric',
        notifications: {
          workoutReminders: true,
          mealReminders: true,
          progressUpdates: true,
          motivationalMessages: true,
        },
        privacy: {
          shareProgress: false,
          shareWorkouts: false,
          allowAnalytics: true,
        },
        theme: 'male',
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date(),
    };
  }

  async updateUserProfile(userId: string, profile: Partial<UserProfile>): Promise<UserProfile> {
    await this.simulateDelay('MEDIUM');
    this.simulateError();

    // Simulate profile update
    const existingProfile = await this.getUserProfile(userId);
    if (!existingProfile) {
      throw new Error('User not found');
    }

    return {
      ...existingProfile,
      ...profile,
      updatedAt: new Date(),
    };
  }

  async createUserProfile(profile: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<UserProfile> {
    await this.simulateDelay('MEDIUM');
    this.simulateError();

    return {
      ...profile,
      id: `user_${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  // Workout Services
  async getWorkoutPlans(userProfile: UserProfile): Promise<WorkoutPlan[]> {
    await this.simulateDelay('MEDIUM');
    this.simulateError();

    // Filter workout plans based on user profile
    const allPlans = this.getMockWorkoutPlans();
    return allPlans.filter(plan => {
      // Filter by gender and goal
      const genderMatch = plan.targetGender === 'unisex' || plan.targetGender === userProfile.gender;
      const goalMatch = plan.fitnessGoal === userProfile.fitnessGoal;
      return genderMatch && goalMatch;
    });
  }

  async getWorkoutPlan(planId: string): Promise<WorkoutPlan | null> {
    await this.simulateDelay('FAST');
    this.simulateError();

    const plans = this.getMockWorkoutPlans();
    return plans.find(plan => plan.id === planId) || null;
  }

  async getExercises(category?: string): Promise<Exercise[]> {
    await this.simulateDelay('MEDIUM');
    this.simulateError();

    const exercises = this.getMockExercises();
    if (category) {
      return exercises.filter(exercise => exercise.category === category);
    }
    return exercises;
  }

  async logWorkoutSession(session: Omit<WorkoutSession, 'id'>): Promise<WorkoutSession> {
    await this.simulateDelay('MEDIUM');
    this.simulateError();

    return {
      ...session,
      id: `session_${Date.now()}`,
    };
  }

  // Nutrition Services
  async getMealPlans(userProfile: UserProfile): Promise<MealPlan[]> {
    await this.simulateDelay('MEDIUM');
    this.simulateError();

    const allPlans = this.getMockMealPlans();
    return allPlans.filter(plan => {
      // Filter by goal and dietary preferences
      return plan.fitnessGoal === userProfile.fitnessGoal;
    });
  }

  async getMealPlan(planId: string): Promise<MealPlan | null> {
    await this.simulateDelay('FAST');
    this.simulateError();

    const plans = this.getMockMealPlans();
    return plans.find(plan => plan.id === planId) || null;
  }

  async logNutrition(log: Omit<FoodLog, 'id'>): Promise<FoodLog> {
    await this.simulateDelay('MEDIUM');
    this.simulateError();

    return {
      ...log,
      id: `nutrition_${Date.now()}`,
    };
  }

  // Progress Services
  async getProgressEntries(userId: string, startDate?: Date, endDate?: Date): Promise<ProgressEntry[]> {
    await this.simulateDelay('MEDIUM');
    this.simulateError();

    const mockEntries = this.getMockProgressEntries(userId);
    
    if (startDate || endDate) {
      return mockEntries.filter(entry => {
        const entryDate = new Date(entry.date);
        const afterStart = !startDate || entryDate >= startDate;
        const beforeEnd = !endDate || entryDate <= endDate;
        return afterStart && beforeEnd;
      });
    }

    return mockEntries;
  }

  async addProgressEntry(entry: Omit<ProgressEntry, 'id' | 'createdAt'>): Promise<ProgressEntry> {
    await this.simulateDelay('MEDIUM');
    this.simulateError();

    return {
      ...entry,
      id: `progress_${Date.now()}`,
      createdAt: new Date(),
    };
  }

  // Mock data generators
  private getMockWorkoutPlans(): WorkoutPlan[] {
    return [
      {
        id: 'plan_1',
        name: 'Beginner Strength Training',
        description: 'Perfect for building foundational strength',
        duration: 8, // weeks
        difficulty: 'beginner',
        targetGender: 'unisex',
        fitnessGoal: 'muscle_gain',

        equipment: ['dumbbells', 'barbell', 'bench'],
        exercises: [],
        tags: ['strength', 'beginner'],
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
      },
      {
        id: 'plan_2',
        name: 'Women\'s Toning Program',
        description: 'Designed specifically for women looking to tone and strengthen',
        duration: 12,
        difficulty: 'intermediate',
        targetGender: 'female',
        fitnessGoal: 'weight_loss',

        equipment: ['dumbbells', 'resistance_bands', 'mat'],
        exercises: [],
        tags: ['toning', 'intermediate'],
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
      },
    ];
  }

  private getMockExercises(): Exercise[] {
    return [
      {
        id: 'ex_1',
        name: 'Push-ups',
        category: 'strength',
        description: 'Classic bodyweight exercise for chest, shoulders, and triceps',
        instructions: [
          'Start in plank position with hands shoulder-width apart',
          'Lower your body until chest nearly touches the floor',
          'Push back up to starting position',
          'Keep your body in a straight line throughout',
        ],
        targetMuscles: ['chest', 'shoulders', 'triceps'],
        equipment: [],
        difficulty: 'beginner',
        mediaUrl: 'https://example.com/pushups.mp4',
        thumbnailUrl: 'https://example.com/pushups.jpg',
        tips: ['Keep core engaged', 'Maintain straight line'],
      },
      {
        id: 'ex_2',
        name: 'Squats',
        category: 'strength',
        description: 'Fundamental lower body exercise',
        instructions: [
          'Stand with feet shoulder-width apart',
          'Lower your body as if sitting back into a chair',
          'Keep your chest up and knees behind toes',
          'Return to standing position',
        ],
        targetMuscles: ['quadriceps', 'glutes', 'hamstrings'],
        equipment: [],
        difficulty: 'beginner',
        mediaUrl: 'https://example.com/squats.mp4',
        thumbnailUrl: 'https://example.com/squats.jpg',
        tips: ['Keep knees aligned', 'Engage glutes'],
      },
    ];
  }

  private getMockMealPlans(): MealPlan[] {
    return [
      {
        id: 'meal_1',
        name: 'High Protein Muscle Building',
        description: 'Designed to support muscle growth and recovery',
        targetGender: 'unisex',
        fitnessGoal: 'muscle_gain',
        duration: 30,
        dailyMealPlans: [],
        averageDailyNutrition: {
          calories: 2500,
          protein: 150,
          carbohydrates: 250,
          fat: 100,
        },
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
      },
      {
        id: 'meal_2',
        name: 'Weight Loss Plan',
        description: 'Balanced nutrition for sustainable weight loss',
        targetGender: 'unisex',
        fitnessGoal: 'weight_loss',
        duration: 30,
        dailyMealPlans: [],
        averageDailyNutrition: {
          calories: 1800,
          protein: 120,
          carbohydrates: 180,
          fat: 70,
        },
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
      },
    ];
  }

  private getMockProgressEntries(userId: string): ProgressEntry[] {
    const entries: ProgressEntry[] = [];
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30); // Last 30 days

    for (let i = 0; i < 10; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i * 3);

      entries.push({
        id: `progress_${i}`,
        userId,
        date,
        weight: 75 - (i * 0.5), // Gradual weight loss
        bodyFatPercentage: 15 - (i * 0.2),
        muscleMass: 60 + (i * 0.1),
        measurements: {
          chest: 100,
          waist: 85 - (i * 0.3),
          hips: 95,
          arms: 35 + (i * 0.1),
          thighs: 55,
        },
        notes: i === 0 ? 'Starting measurements' : `Week ${Math.floor(i / 2) + 1} progress`,
        createdAt: date,
      });
    }

    return entries;
  }
}

// Export singleton instance
export const mockApiService = new MockApiService();