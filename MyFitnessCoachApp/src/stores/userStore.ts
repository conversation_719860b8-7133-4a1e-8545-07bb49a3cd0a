import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserProfile, OnboardingData } from '../types/user';

interface UserState {
  // Auth state
  isAuthenticated: boolean;
  isLoading: boolean;
  authToken?: string;
  refreshToken?: string;
  
  // User data
  user?: UserProfile;
  onboardingData?: OnboardingData;
  
  // Onboarding state
  isOnboardingCompleted: boolean;
  currentOnboardingStep: number;
  
  // Actions
  login: (email: string, _password: string) => Promise<void>;
  register: (userData: Partial<UserProfile>) => Promise<void>;
  logout: () => void;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  updateOnboardingData: (data: Partial<OnboardingData>) => void;
  completeOnboarding: () => void;
  setCurrentOnboardingStep: (step: number) => void;
  refreshAuthToken: () => Promise<void>;
  setLoading: (loading: boolean) => void;
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      // Initial state
      isAuthenticated: false,
      isLoading: false,
      authToken: undefined,
      refreshToken: undefined,
      user: undefined,
      onboardingData: undefined,
      isOnboardingCompleted: false,
      currentOnboardingStep: 0,

      // Actions
      login: async (email: string, _password: string) => {
        set({ isLoading: true });
        try {
          // TODO: Replace with actual API call
          const mockResponse = {
            user: {
              id: '1',
              name: 'John Doe',
              email,
              age: 30,
              gender: 'male' as const,
              height: 180,
              weight: 75,
              activityLevel: 'moderately_active' as const,
              fitnessGoal: 'weight_loss' as const,
              preferences: {
                theme: 'neutral' as const,
                units: 'metric' as const,
                notifications: {
                  workoutReminders: true,
                  mealReminders: true,
                  progressUpdates: true,
                  motivationalMessages: true,
                },
                privacy: {
                  shareWorkouts: false,
                  shareProgress: false,
                  allowAnalytics: true,
                },
              },
              createdAt: new Date(),
              updatedAt: new Date(),
            },
            authToken: 'mock-auth-token',
            refreshToken: 'mock-refresh-token',
          };

          set({
            isAuthenticated: true,
            user: mockResponse.user,
            authToken: mockResponse.authToken,
            refreshToken: mockResponse.refreshToken,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      register: async (userData: Partial<UserProfile>) => {
        set({ isLoading: true });
        try {
          // TODO: Replace with actual API call
          const mockUser: UserProfile = {
            id: Date.now().toString(),
            name: userData.name || 'New User',
            email: userData.email || '',
            age: userData.age || 25,
            gender: userData.gender || 'male',
            height: userData.height || 170,
            weight: userData.weight || 70,
            activityLevel: userData.activityLevel || 'moderately_active',
            fitnessGoal: userData.fitnessGoal || 'maintenance',
            preferences: {
              theme: 'neutral',
              units: 'metric',
              notifications: {
                workoutReminders: true,
                mealReminders: true,
                progressUpdates: true,
                motivationalMessages: true,
              },
              privacy: {
                shareWorkouts: false,
                shareProgress: false,
                allowAnalytics: true,
              },
            },
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          set({
            isAuthenticated: true,
            user: mockUser,
            authToken: 'mock-auth-token',
            refreshToken: 'mock-refresh-token',
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: () => {
        set({
          isAuthenticated: false,
          user: undefined,
          authToken: undefined,
          refreshToken: undefined,
          onboardingData: undefined,
          isOnboardingCompleted: false,
          currentOnboardingStep: 0,
        });
      },

      updateProfile: async (updates: Partial<UserProfile>) => {
        const currentUser = get().user;
        if (!currentUser) return;

        set({ isLoading: true });
        try {
          // TODO: Replace with actual API call
          const updatedUser = {
            ...currentUser,
            ...updates,
            updatedAt: new Date(),
          };

          set({
            user: updatedUser,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      updateOnboardingData: (data: Partial<OnboardingData>) => {
        const currentData = get().onboardingData || {
          step: 0,
          totalSteps: 4,
          isCompleted: false,
          userData: {},
        };
        set({
          onboardingData: {
            ...currentData,
            ...data,
          },
        });
      },

      completeOnboarding: () => {
        const { onboardingData, user } = get();
        if (onboardingData && user && onboardingData.userData) {
          // Merge onboarding data with user profile
          const updatedUser: UserProfile = {
            ...user,
            ...onboardingData.userData,
            updatedAt: new Date(),
          };

          set({
            user: updatedUser,
            isOnboardingCompleted: true,
            onboardingData: undefined,
          });
        }
      },

      setCurrentOnboardingStep: (step: number) => {
        set({ currentOnboardingStep: step });
      },

      refreshAuthToken: async () => {
        const { refreshToken } = get();
        if (!refreshToken) return;

        try {
          // TODO: Replace with actual API call
          const newAuthToken = 'new-mock-auth-token';
          set({ authToken: newAuthToken });
        } catch (error) {
          // If refresh fails, logout user
          get().logout();
          throw error;
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'user-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        authToken: state.authToken,
        refreshToken: state.refreshToken,
        user: state.user,
        isOnboardingCompleted: state.isOnboardingCompleted,
      }),
    }
  )
);