import { AppRegistry } from 'react-native';
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { name as appName } from './app.json';

// Simple web app component without navigation for now
const WebApp = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>My Fitness Coach App</Text>
      <Text style={styles.subtitle}>Web Preview</Text>
      <Text style={styles.description}>
        Welcome to My Fitness Coach! This is the web version of your React Native app.
      </Text>
      <Text style={styles.note}>
        Note: Full navigation and features are optimized for mobile devices.
        Use the iOS or Android simulators for the complete experience.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    color: '#444',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  note: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 20,
  },
});

// Register the app for web
AppRegistry.registerComponent(appName, () => WebApp);

// Run the app
AppRegistry.runApplication(appName, {
  initialProps: {},
  rootTag: document.getElementById('root'),
});